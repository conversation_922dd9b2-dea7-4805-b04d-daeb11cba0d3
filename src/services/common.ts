import createFetchClient, { type Middleware } from "openapi-fetch";
import createClient from "openapi-react-query";
import { type paths } from "../openapi-spec";
import { camelCaseKeys } from "@/lib/camelCase";
import { snakeCaseKeys } from "@/lib/snakeCase";

const fetchClient = createFetchClient<paths>({
  baseUrl: import.meta.env.VITE_BACKEND_URL,
  credentials: "include",
});

const camelCaseMiddleware: Middleware = {
  async onResponse({ response }) {
    if (response.ok) {
      const data = await response.json();
      if (data && typeof data === "object") {
        const camelCasedData = camelCaseKeys(data);
        return new Response(JSON.stringify(camelCasedData), {
          status: response.status,
          statusText: response.statusText,
          headers: response.headers,
        });
      }
    }
    return response;
  },
};

const snakeCaseMiddleware: Middleware = {
  async onRequest(ctx) {
    const opts = ctx.options as RequestInit & {
      body?: unknown;
      headers?: HeadersInit;
      json?: unknown;
    };

    if (opts.json && typeof opts.json === "object") {
      opts.body = JSON.stringify(snakeCaseKeys(opts.json));
      opts.headers = {
        "Content-Type": "application/json",
        ...(opts.headers || {}),
      };
      delete opts.json;
    } else if (opts.body && !(opts.body instanceof FormData)) {
      const payload =
        typeof opts.body === "string"
          ? JSON.parse(opts.body)
          : (opts.body as object);
      opts.body = JSON.stringify(snakeCaseKeys(payload));
      opts.headers = {
        "Content-Type": "application/json",
        ...(opts.headers || {}),
      };
    }
  },
};

fetchClient.use(snakeCaseMiddleware);
fetchClient.use(camelCaseMiddleware);

export const $api = createClient(fetchClient);
