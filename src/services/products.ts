import { $api } from "./common";
import { snakeCaseKeys } from "@/lib/snakeCase";
import type { ProductListResponse, Product } from "@/types/products";

export interface ListProductsParams {
  page?: number;
  limit?: number;
}

export function useProductsList(
  params: ListProductsParams = { page: 1, limit: 10 },
) {
  const query = $api.useQuery("get", "/api/products", {
    params: { query: snakeCaseKeys(params) },
  });
  return { ...query, data: query.data as ProductListResponse | undefined };
}

export function useProduct(id: number) {
  const query = $api.useQuery("get", "/api/products/{id}", {
    params: { path: { id } },
  });
  return { ...query, data: query.data as Product | undefined };
}

export function useCreateProduct() {
  return $api.useMutation("post", "/api/products");
}
