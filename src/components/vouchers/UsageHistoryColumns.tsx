import { Badge } from "@/components/ui/badge";
import type { ColumnDef } from "@tanstack/react-table";
import type { VoucherUsageHistoryRecord } from "@/types/vouchers";

export function createUsageHistoryColumns(): ColumnDef<
  VoucherUsageHistoryRecord,
  unknown
>[] {
  return [
    {
      accessorKey: "userId",
      header: "USER ID",
      cell: (info) => (
        <span className="font-medium">#{info.getValue<number>()}</span>
      ),
    },
    {
      accessorKey: "fullName",
      header: "NAME",
    },
    {
      accessorKey: "email",
      header: "EMAIL",
    },
    {
      accessorKey: "orderId",
      header: "ORDER ID",
      cell: (info) => <>#{info.getValue<number>()}</>,
    },
    {
      accessorKey: "usedAt",
      header: "USED AT",
      cell: (info) =>
        new Date(info.getValue<string>()).toLocaleDateString("en-US", {
          year: "numeric",
          month: "long",
          day: "numeric",
          hour: "2-digit",
          minute: "2-digit",
        }),
    },
    {
      accessorKey: "orderAmount",
      header: "ORDER AMOUNT",
      cell: (info) =>
        new Intl.NumberFormat("vi-VN", {
          style: "currency",
          currency: "VND",
        }).format(info.getValue<number>()),
    },
    {
      accessorKey: "status",
      header: "STATUS",
      cell: (info) => (
        <Badge
          className={
            info.getValue<string>() === "SUCCESS"
              ? "bg-green-100 text-green-800"
              : "bg-red-100 text-red-800"
          }
        >
          {info.getValue<string>()}
        </Badge>
      ),
    },
    {
      accessorKey: "type",
      header: "TYPE",
      cell: (info) => (
        <Badge variant="outline" className="uppercase">
          {info.getValue<string>()}
        </Badge>
      ),
    },
  ];
}
