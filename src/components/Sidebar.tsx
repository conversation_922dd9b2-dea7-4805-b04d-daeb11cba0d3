import React from "react";
import { TicketIcon, CreditCardIcon, BarChart3 } from "lucide-react";
import { cn } from "../lib/utils";
import { useNavigate } from "@tanstack/react-router";

interface SidebarProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
}

export const Sidebar: React.FC<SidebarProps> = ({ activeTab, onTabChange }) => {
  const navigate = useNavigate();
  const menuItems = [
    { id: "dashboard", label: "Dashboard", icon: BarChart3, path: "/" },
    { id: "vouchers", label: "Vouchers", icon: TicketIcon, path: "/vouchers" },
    { id: "orders", label: "Orders", icon: CreditCardIcon, path: "/orders" },
  ];

  let headerTitle = "";
  let headerDescription = "";

  switch (activeTab) {
    case "vouchers":
      headerTitle = "Voucher Management";
      headerDescription = "Manage and monitor all your discount vouchers";
      break;
    case "calculator":
      headerTitle = "Order Calculator";
      headerDescription = "Calculate orders with coupon codes";
      break;
    case "create":
      headerTitle = "Create Voucher";
      headerDescription = "Add a new discount voucher to your system";
      break;
    case "edit":
      headerTitle = "Edit Voucher";
      headerDescription = "Update voucher information";
      break;
    default:
      headerTitle = "Voucher Management";
      headerDescription = "Manage and monitor all your discount vouchers";
  }

  return (
    <div className="sticky top-0 flex flex-col h-screen">
      <div className="w-64 bg-white shadow-sm border-r border-gray-200 flex flex-col h-full">
        <div className="p-7 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <CreditCardIcon className="w-5 h-5 text-white" />
            </div>
            <div>
              <h1 className="text-lg font-semibold text-gray-900">
                ZaloPay Admin
              </h1>
            </div>
          </div>
        </div>

        <nav className="flex-1 p-4">
          <div className="space-y-2">
            <div className="text-xs font-medium text-gray-500 uppercase tracking-wider mb-3">
              VOUCHER MANAGEMENT
            </div>
            {menuItems.map((item) => {
              const Icon = item.icon;
              const isActive =
                activeTab === item.id ||
                (item.id === "vouchers" && activeTab === "vouchers");
              return (
                <button
                  key={item.id}
                  onClick={() => {
                    onTabChange(item.id);
                    navigate({ to: item.path as any });
                  }}
                  className={cn(
                    "w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors text-sm",
                    isActive
                      ? "bg-blue-50 text-blue-700"
                      : "text-gray-600 hover:bg-gray-50 hover:text-gray-900",
                  )}
                >
                  <Icon className="w-5 h-5" />
                  <span className="font-medium">{item.label}</span>
                </button>
              );
            })}
          </div>
        </nav>
      </div>
    </div>
  );
};
