import React from "react";
import { Search, ChevronDown, ArrowLeft } from "lucide-react";
import { useNavigate } from "@tanstack/react-router";
import { useContext } from "react";
import { LayoutContext } from "@/routes/__root";

interface HeaderProps {
  activeTab?: string;
}

export const Header: React.FC<HeaderProps> = ({ activeTab = "vouchers" }) => {
  let headerTitle = "";
  let headerDescription = "";

  switch (activeTab) {
    case "vouchers":
      headerTitle = "Voucher Management";
      headerDescription = "Manage and monitor all your discount vouchers";
      break;
    case "calculator":
      headerTitle = "Order Calculator";
      headerDescription = "Calculate orders with coupon codes";
      break;
    case "create":
      headerTitle = "Create Voucher";
      headerDescription = "Add a new discount voucher to your system";
      break;
    case "edit":
      headerTitle = "Edit Voucher";
      headerDescription = "Update voucher information";
      break;
    case "dashboard":
      headerTitle = "Dashboard";
      headerDescription =
        "Efficiently manage vouchers and process orders for ZaloPay platform";
      break;
    case "orders":
      headerTitle = "Orders Management";
      headerDescription = "Manage and monitor all orders";
      break;
    case "detail":
      headerTitle = "Voucher Detail";
      headerDescription = "View full information of your voucher";
      break;
    default:
      headerTitle = "Voucher Management";
      headerDescription = "Manage and monitor all your discount vouchers";
  }

  const navigate = useNavigate();
  const { setActiveTab } = useContext(LayoutContext);
  const showBack = activeTab === "detail" || activeTab === "edit";

  return (
    <div className="sticky top-0 bg-white border-b border-gray-200 px-6 py-4 z-10">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          {showBack && (
            <button
              onClick={() => {
                if (activeTab === "edit") {
                  setActiveTab("detail");
                  navigate({ to: ".." });
                } else {
                  setActiveTab("vouchers");
                  navigate({ to: "/vouchers" });
                }
              }}
              className="p-2 rounded-md hover:bg-gray-100"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
          )}
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{headerTitle}</h1>
            <p className="text-gray-600">{headerDescription}</p>
          </div>
        </div>

        <div className="flex items-center space-x-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search..."
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg text-sm w-64 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <div className="flex items-center space-x-2 cursor-pointer hover:bg-gray-50 rounded-lg p-2">
            <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
              <span className="text-sm font-medium text-gray-600">AU</span>
            </div>
            <span className="text-sm font-medium text-gray-900">
              Admin User
            </span>
            <ChevronDown className="w-4 h-4 text-gray-600" />
          </div>
        </div>
      </div>
    </div>
  );
};
