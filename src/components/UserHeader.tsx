import { CreditCardIcon, ChevronDown } from "lucide-react";
import { useUser } from "@/services/user";
import { Skeleton } from "@/components/ui/skeleton";

function initials(name?: string) {
  if (!name) return "";
  const parts = name.split(" ");
  const first = parts[0]?.charAt(0) ?? "";
  const last = parts[1]?.charAt(0) ?? "";
  return `${first}${last}`.toUpperCase();
}

export function UserHeader() {
  const user = useUser();

  return (
    <header className="sticky top-0 bg-white border-b border-gray-200 px-6 py-4 z-10">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
            <CreditCardIcon className="w-5 h-5 text-white" />
          </div>
          <h1 className="text-lg font-semibold text-gray-900">ZaloPay</h1>
        </div>
        <div className="flex items-center gap-2">
          {user.isPending ? (
            <Skeleton className="h-5 w-20" />
          ) : (
            <>
              <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                <span className="text-xs font-medium text-gray-600">
                  {initials(user.data?.full_name)}
                </span>
              </div>
              <span className="text-sm font-medium text-gray-900">
                {user.data?.full_name ?? ""}
              </span>
            </>
          )}
          <ChevronDown className="w-4 h-4 text-gray-600" />
        </div>
      </div>
    </header>
  );
}
