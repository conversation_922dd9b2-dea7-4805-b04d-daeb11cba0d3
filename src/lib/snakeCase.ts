export function toSnakeCase(str: string): string {
  return str
    .replace(/([A-Z])/g, "_$1")
    .replace(/-/g, "_")
    .toLowerCase();
}

function snakelize(value: unknown): unknown {
  if (Array.isArray(value)) {
    return value.map(snakelize);
  }
  if (value && typeof value === "object" && value.constructor === Object) {
    const result: Record<string, unknown> = {};
    for (const [key, val] of Object.entries(value as Record<string, unknown>)) {
      result[toSnakeCase(key)] = snakelize(val);
    }
    return result;
  }
  return value;
}

export type SnakeCase<S extends string> = S extends `${infer T}${infer U}`
  ? U extends Uncapitalize<U>
    ? `${Lowercase<T>}${SnakeCase<U>}`
    : `${Lowercase<T>}_${SnakeCase<Uncapitalize<U>>}`
  : S;

export type SnakeCased<T> =
  T extends Array<infer U>
    ? SnakeCased<U>[]
    : T extends object
      ? { [K in keyof T as SnakeCase<K & string>]: SnakeCased<T[K]> }
      : T;

export function snakeCaseKeys<T>(input: T): SnakeCased<T> {
  return snakelize(input) as SnakeCased<T>;
}
