export function toCamelCase(str: string): string {
  return str.replace(/_([a-z])/g, (_, c) => c.toUpperCase());
}

function camelize(value: unknown): unknown {
  if (Array.isArray(value)) {
    return value.map(camelize);
  }
  if (value && typeof value === "object" && value.constructor === Object) {
    const result: Record<string, unknown> = {};
    for (const [key, val] of Object.entries(value as Record<string, unknown>)) {
      result[toCamelCase(key)] = camelize(val);
    }
    return result;
  }
  return value;
}

export type CamelCase<S extends string> = S extends `${infer P}_${infer U}`
  ? `${P}${Capitalize<CamelCase<U>>}`
  : S;

export type CamelCased<T> =
  T extends Array<infer U>
    ? CamelCased<U>[]
    : T extends object
      ? { [K in keyof T as CamelCase<K & string>]: CamelCased<T[K]> }
      : T;

export function camelCaseKeys<T>(input: T): CamelCased<T> {
  return camelize(input) as Camel<PERSON>ased<T>;
}
