import { useNavigate, useParams } from "@tanstack/react-router";
import { useContext, useEffect, useMemo, useState } from "react";
import { LayoutContext } from "@/routes/__root";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { createUsageHistoryColumns } from "@/components/vouchers/UsageHistoryColumns";
import {
  Edit,
  Trash2,
  Clock,
  Users,
  Tag,
  Calendar,
  Settings,
  TrendingUp,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
import { useVoucher } from "@/services/vouchers";
import type { Voucher, TimeRestriction } from "@/types/vouchers";

function getPageNumbers(current: number, total: number) {
  if (total <= 5) {
    return Array.from({ length: total }, (_, i) => i + 1);
  }
  const pages: (number | string)[] = [1];
  let start = current - 1;
  let end = current + 1;

  if (start <= 2) {
    start = 2;
    end = start + 2;
  }

  if (end >= total - 1) {
    end = total - 1;
    start = end - 2;
  }

  if (start > 2) pages.push("...");
  for (let i = start; i <= end; i++) pages.push(i);
  if (end < total - 1) pages.push("...");
  pages.push(total);
  return pages;
}

function formatCurrency(amount?: number) {
  if (amount === undefined) return "-";
  return new Intl.NumberFormat("vi-VN", {
    style: "currency",
    currency: "VND",
  }).format(amount);
}

function formatDate(dateString?: string) {
  if (!dateString) return "-";
  return new Date(dateString).toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  });
}

const dayNames = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
const dayNamesFull = [
  "Sunday",
  "Monday",
  "Tuesday",
  "Wednesday",
  "Thursday",
  "Friday",
  "Saturday",
];
const monthNames = [
  "January",
  "February",
  "March",
  "April",
  "May",
  "June",
  "July",
  "August",
  "September",
  "October",
  "November",
  "December",
];

function formatRecurrence(r: TimeRestriction) {
  if (!r.recurrencePattern) return "";
  const parts = [r.recurrencePattern];
  if (r.recurrenceMonth !== undefined) {
    parts.push(monthNames[r.recurrenceMonth - 1] ?? "");
  }
  if (r.recurrenceDayOfMonth !== undefined) {
    parts.push(`day ${r.recurrenceDayOfMonth}`);
  }
  if (r.recurrenceDayOfWeek !== undefined) {
    parts.push(dayNamesFull[r.recurrenceDayOfWeek] ?? "");
  }
  return parts.join(" ");
}

function getStatusColor(status?: string) {
  switch (status) {
    case "Active":
      return "bg-green-100 text-green-800";
    case "Expired":
      return "bg-red-100 text-red-800";
    case "Upcoming":
      return "bg-yellow-100 text-yellow-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
}

function getDiscountDisplay(voucher: Voucher) {
  if (voucher.discountType?.id === 1) {
    return `${voucher.discountValue}%`;
  }
  return formatCurrency(voucher.discountValue);
}

export default function VoucherDetail() {
  const params = useParams({ from: "/vouchers/$id" });
  const navigate = useNavigate();
  const { setActiveTab } = useContext(LayoutContext);

  useEffect(() => {
    setActiveTab("detail");
  }, [setActiveTab]);

  const {
    data: rawVoucher,
    isPending,
    isError,
  } = useVoucher(Number(params.id));
  const voucher = rawVoucher as Voucher | undefined;
  const [pagination, setPagination] = useState({ pageIndex: 0, pageSize: 10 });

  const recentUsage = useMemo(() => {
    if (!voucher?.userUsage) return [];
    return voucher.userUsage.flatMap((u) =>
      (u.orders ?? []).map((o) => ({
        userId: u.userId,
        fullName: u.fullName,
        email: u.email,
        type: u.type,
        orderId: o.orderId,
        usedAt: o.usedAt,
        orderAmount: o.orderAmount,
        status: o.status,
      })),
    );
  }, [voucher?.userUsage]);
  const totalPages = Math.ceil(recentUsage.length / pagination.pageSize);

  const columns = useMemo(() => createUsageHistoryColumns(), []);
  const table = useReactTable({
    data: recentUsage,
    columns,
    pageCount: totalPages,
    state: { pagination },
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  });
  const pageNumbers = useMemo(
    () => getPageNumbers(pagination.pageIndex + 1, totalPages),
    [pagination.pageIndex, totalPages],
  );

  const includedCategories = voucher?.productRestrictions?.filter(
    (r) => r.categoryId && r.isIncluded,
  );
  const excludedCategories = voucher?.productRestrictions?.filter(
    (r) => r.categoryId && !r.isIncluded,
  );
  const includedProducts = voucher?.productRestrictions?.filter(
    (r) => r.productId && r.isIncluded,
  );
  const excludedProducts = voucher?.productRestrictions?.filter(
    (r) => r.productId && !r.isIncluded,
  );

  if (isPending) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        Loading...
      </div>
    );
  }

  if (isError || !voucher) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center space-y-4">
          <h1 className="text-2xl font-bold text-gray-900">
            Voucher Not Found
          </h1>
          <Button
            onClick={() => {
              setActiveTab("vouchers");
              navigate({ to: "/vouchers" });
            }}
          >
            Back to Vouchers
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white py-12 px-8">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-5xl font-bold mb-2">{voucher.voucherCode}</h1>
              <p className="text-xl opacity-90">{voucher.title}</p>
              <div className="flex items-center gap-4 mt-4">
                <Badge className={getStatusColor(voucher.status)}>
                  {voucher.status}
                </Badge>
                <span className="text-sm opacity-80">
                  Created: {formatDate(voucher.createdAt)}
                </span>
              </div>
            </div>
            <div className="text-right">
              <div className="text-6xl font-bold text-white">
                {getDiscountDisplay(voucher)}
              </div>
              <div className="text-lg opacity-80 mt-2 bg-white/20 px-4 py-1 rounded-full inline-block">
                OFF
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Tag className="h-5 w-5" /> Basic Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between">
                <span className="text-gray-600">Voucher Code:</span>
                <span className="font-medium">{voucher.voucherCode}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Discount Type:</span>
                <Badge variant="outline">
                  {voucher.discountType?.typeName}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Usage Method:</span>
                <Badge
                  className={
                    voucher.usageMethod === "AUTO"
                      ? "bg-green-100 text-green-800"
                      : "bg-yellow-100 text-yellow-800"
                  }
                >
                  {voucher.usageMethod}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Min Order:</span>
                <span className="font-medium">
                  {formatCurrency(voucher.minOrderAmount)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Max Discount:</span>
                <span className="font-medium">
                  {voucher.maxDiscountAmount
                    ? formatCurrency(voucher.maxDiscountAmount)
                    : "No limit"}
                </span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" /> Validity & Usage
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between">
                <span className="text-gray-600">Valid From:</span>
                <span className="font-medium">
                  {formatDate(voucher.validFrom)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Valid Until:</span>
                <span className="font-medium">
                  {formatDate(voucher.validUntil)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Current Uses:</span>
                <span className="font-medium">
                  {voucher.currentUsageCount?.toLocaleString() || 0}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Max Uses:</span>
                <span className="font-medium">
                  {voucher.maxUsageCount?.toLocaleString() || "Unlimited"}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Per User Limit:</span>
                <span className="font-medium">
                  {voucher.maxUsagePerUser || "No limit"}
                </span>
              </div>
            </CardContent>
          </Card>
        </div>

        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" /> Performance
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              <div className="bg-blue-50 rounded-lg p-4 text-center">
                <div className="text-3xl font-bold text-blue-600 mb-1">
                  {voucher.currentUsageCount ?? 0}
                </div>
                <div className="text-sm text-blue-600 font-medium">
                  Total Uses
                </div>
              </div>
              <div className="bg-green-50 rounded-lg p-4 text-center">
                <div className="text-3xl font-bold text-green-600 mb-1">
                  {formatCurrency(voucher.totalSavings || 0)}
                </div>
                <div className="text-sm text-green-600 font-medium">
                  Total Savings
                </div>
              </div>
              <div className="bg-purple-50 rounded-lg p-4 text-center">
                <div className="text-3xl font-bold text-purple-600 mb-1">
                  {voucher.uniqueUsers ?? 0}
                </div>
                <div className="text-sm text-purple-600 font-medium">
                  Unique Users
                </div>
              </div>
              <div className="bg-orange-50 rounded-lg p-4 text-center">
                <div className="text-3xl font-bold text-orange-600 mb-1">
                  {voucher.maxUsageCount
                    ? voucher.maxUsageCount - (voucher.currentUsageCount ?? 0)
                    : 0}
                </div>
                <div className="text-sm text-orange-600 font-medium">
                  Remaining Uses
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {voucher.description && (
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>Description</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700 leading-relaxed">
                {voucher.description}
              </p>
            </CardContent>
          </Card>
        )}

        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" /> User Eligibility Rules
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium mb-3">Eligibility Type</h4>
                <Badge className="bg-blue-100 text-blue-800">
                  {voucher.userEligibilityType ?? "All Users"}
                </Badge>
              </div>
              <div>
                <h4 className="font-medium mb-3">Account Requirements</h4>
                {voucher.userEligibility &&
                voucher.userEligibility.length > 0 ? (
                  <div className="space-y-2 text-sm">
                    {voucher.userEligibility.map((rule, idx) => (
                      <div key={idx} className="space-y-1">
                        {rule.minAccountAgeDays !== undefined && (
                          <div>
                            Min Account Age: {rule.minAccountAgeDays} days
                          </div>
                        )}
                        {rule.maxAccountAgeDays !== undefined && (
                          <div>
                            Max Account Age: {rule.maxAccountAgeDays} days
                          </div>
                        )}
                        {rule.minPreviousOrders !== undefined && (
                          <div>
                            Min Previous Orders: {rule.minPreviousOrders}
                          </div>
                        )}
                        {rule.maxPreviousOrders !== undefined && (
                          <div>
                            Max Previous Orders: {rule.maxPreviousOrders}
                          </div>
                        )}
                        {rule.userType && <div>User Type: {rule.userType}</div>}
                        {rule.userId && <div>User ID: {rule.userId}</div>}
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-sm text-gray-600">No requirements</div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" /> Time Restrictions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {voucher.timeRestrictions?.some(
                (r) => r.allowedDaysOfWeek && r.allowedDaysOfWeek.length > 0,
              ) && (
                <div>
                  <h4 className="font-medium mb-3">Days of Week</h4>
                  {voucher.timeRestrictions!.map((r, idx) => (
                    <div key={idx} className="flex flex-wrap gap-2 mb-2">
                      {r.allowedDaysOfWeek && r.allowedDaysOfWeek.length > 0 ? (
                        r.allowedDaysOfWeek.map((d) => (
                          <Badge key={d} variant="outline" className="text-xs">
                            {dayNames[d] ?? d}
                          </Badge>
                        ))
                      ) : (
                        <span className="text-sm text-gray-600">Any day</span>
                      )}
                    </div>
                  ))}
                </div>
              )}

              {voucher.timeRestrictions?.some(
                (r) =>
                  r.allowedHoursStart !== undefined &&
                  r.allowedHoursEnd !== undefined,
              ) && (
                <div>
                  <h4 className="font-medium mb-3">Hours</h4>
                  {voucher.timeRestrictions!.map((r, idx) => (
                    <div key={idx} className="text-sm text-gray-600">
                      {r.allowedHoursStart !== undefined &&
                      r.allowedHoursEnd !== undefined
                        ? `${r.allowedHoursStart}:00 - ${r.allowedHoursEnd}:00`
                        : "Available 24/7"}
                    </div>
                  ))}
                </div>
              )}

              {voucher.timeRestrictions?.some(
                (r) =>
                  r.recurrencePattern ||
                  r.recurrenceDayOfMonth !== undefined ||
                  r.recurrenceDayOfWeek !== undefined ||
                  r.recurrenceMonth !== undefined,
              ) && (
                <div>
                  <h4 className="font-medium mb-3">Recurring</h4>
                  {voucher.timeRestrictions!.map((r, idx) => (
                    <div key={idx} className="text-sm text-gray-600">
                      {formatRecurrence(r)}
                    </div>
                  ))}
                </div>
              )}

              {voucher.timeRestrictions?.some(
                (r) => r.specificDates && r.specificDates.length > 0,
              ) && (
                <div>
                  <h4 className="font-medium mb-3">Specific Dates</h4>
                  {voucher.timeRestrictions!.map((r, idx) => (
                    <div key={idx} className="flex flex-wrap gap-2 mb-2">
                      {r.specificDates?.map((d, i) => (
                        <Badge
                          key={d ?? i}
                          variant="outline"
                          className="text-xs"
                        >
                          {formatDate(d)}
                        </Badge>
                      ))}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Product & Category Restrictions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {includedCategories && includedCategories.length > 0 && (
                <div>
                  <h4 className="font-medium mb-3">Included Categories</h4>
                  <div className="space-y-2">
                    {includedCategories.map((c) => (
                      <Badge
                        key={c.id}
                        className="bg-green-100 text-green-800 mr-2"
                      >
                        {c.categoryName ?? `Category ${c.categoryId}`}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {excludedCategories && excludedCategories.length > 0 && (
                <div>
                  <h4 className="font-medium mb-3">Excluded Categories</h4>
                  <div className="space-y-2">
                    {excludedCategories.map((c) => (
                      <Badge
                        key={c.id}
                        className="bg-red-100 text-red-800 mr-2"
                      >
                        {c.categoryName ?? `Category ${c.categoryId}`}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {includedProducts && includedProducts.length > 0 && (
                <div>
                  <h4 className="font-medium mb-3">Included Products</h4>
                  <div className="space-y-2">
                    {includedProducts.map((p) => (
                      <Badge
                        key={p.id}
                        className="bg-green-100 text-green-800 mr-2"
                      >
                        {p.productName ?? `Product ${p.productId}`}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {excludedProducts && excludedProducts.length > 0 && (
                <div>
                  <h4 className="font-medium mb-3">Excluded Products</h4>
                  <div className="space-y-2">
                    {excludedProducts.map((p) => (
                      <Badge
                        key={p.id}
                        className="bg-red-100 text-red-800 mr-2"
                      >
                        {p.productName ?? `Product ${p.productId}`}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {(!includedCategories || includedCategories.length === 0) &&
                (!excludedCategories || excludedCategories.length === 0) &&
                (!includedProducts || includedProducts.length === 0) &&
                (!excludedProducts || excludedProducts.length === 0) && (
                  <div className="text-sm text-gray-600">No restrictions</div>
                )}
            </div>
          </CardContent>
        </Card>

        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Recent Usage History</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="relative overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  {table.getHeaderGroups().map((headerGroup) => (
                    <tr key={headerGroup.id}>
                      {headerGroup.headers.map((header) => (
                        <th
                          key={header.id}
                          className="text-left py-3 px-4 font-medium text-gray-500 uppercase text-xs tracking-wider"
                        >
                          {header.isPlaceholder
                            ? null
                            : flexRender(
                                header.column.columnDef.header,
                                header.getContext(),
                              )}
                        </th>
                      ))}
                    </tr>
                  ))}
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {table.getRowModel().rows.map((row) => (
                    <tr key={row.id} className="hover:bg-gray-50">
                      {row.getVisibleCells().map((cell) => (
                        <td key={cell.id} className="py-4 px-4">
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext(),
                          )}
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            <div className="flex items-center justify-between mt-6">
              <span className="text-sm text-gray-500">
                Page {table.getState().pagination.pageIndex + 1} of{" "}
                {table.getPageCount()}
              </span>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="px-3 py-1"
                  onClick={() => table.previousPage()}
                  disabled={!table.getCanPreviousPage()}
                >
                  <ChevronLeft className="w-4 h-4" /> Previous
                </Button>
                {pageNumbers.map((p, idx) =>
                  typeof p === "number" ? (
                    <Button
                      key={p}
                      variant={
                        p - 1 === table.getState().pagination.pageIndex
                          ? "default"
                          : "outline"
                      }
                      size="sm"
                      className="px-3 py-1"
                      onClick={() => table.setPageIndex(p - 1)}
                    >
                      {p}
                    </Button>
                  ) : (
                    <span key={`ellipsis-${idx}`} className="px-2">
                      ...
                    </span>
                  ),
                )}
                <Button
                  variant="outline"
                  size="sm"
                  className="px-3 py-1"
                  onClick={() => table.nextPage()}
                  disabled={!table.getCanNextPage()}
                >
                  Next <ChevronRight className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
        <div className="flex flex-wrap gap-4 justify-center">
          <Button
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3"
            onClick={() => navigate({ to: `/vouchers/${voucher.id}/edit` })}
          >
            <Edit className="h-4 w-4 mr-2" /> Edit Voucher
          </Button>
          <Button
            variant="outline"
            className="border-red-500 text-red-600 hover:bg-red-50 px-6 py-3"
          >
            <Trash2 className="h-4 w-4 mr-2" /> Delete Voucher
          </Button>
        </div>
      </div>
    </div>
  );
}
