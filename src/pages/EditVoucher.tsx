import { useState, useEffect, useContext } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { format } from "date-fns";
import { useNavigate, useParams } from "@tanstack/react-router";
import { LayoutContext } from "@/routes/__root";
import { CalendarIcon, Lock, Plus, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { CurrencyInput } from "@/components/ui/currency-input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Checkbox } from "@/components/ui/checkbox";
import type {
  CouponDetail,
  TimeRestriction as CouponTimeRestriction,
  UserEligibilityRule as CouponUserEligibility,
  ProductRestriction as CouponProductRestriction,
  UpdateCouponRequest,
} from "@/types/vouchers";
import { useCoupon, useUpdateCoupon } from "@/services/vouchers";
import { cn } from "@/lib/utils";

const editCouponSchema = z.object({
  title: z.string().min(1, "Title is required"),
  description: z.string().min(1, "Description is required"),
  discountTypeId: z.number().int(),
  discountValue: z.number().min(0, "Discount value must be positive"),
  usageMethod: z.string(),
  validFrom: z.date(),
  validUntil: z.date(),
  maxUsageCount: z.number().min(1).optional(),
  maxUsagePerUser: z.number().min(1).optional(),
  minOrderAmount: z.number().min(0),
  maxDiscountAmount: z.number().min(0).nullish(),
  status: z.enum(["ACTIVE", "INACTIVE", "EXPIRED", "PAUSED"]),
});

type EditCouponForm = z.infer<typeof editCouponSchema>;

const BasicInfo = ({
  coupon,
  form,
}: {
  coupon?: CouponDetail;
  form: ReturnType<typeof useForm<EditCouponForm>>;
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Basic Information</CardTitle>
      </CardHeader>
      <CardContent className="grid grid-cols-1 lg:grid-cols-2 gap-6 items-start">
        <div>
          <Label
            htmlFor="couponCode"
            className="text-sm font-medium text-gray-700"
          >
            Coupon Code
          </Label>
          <div className="mt-1 relative">
            <Input
              id="couponCode"
              value={coupon?.couponCode}
              disabled
              className="bg-gray-50 text-gray-500 pr-10"
            />
            <Lock className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          </div>
          <p className="mt-1 text-xs text-gray-500">
            Coupon code cannot be changed once created
          </p>
        </div>
        <FormField
          control={form.control}
          name="status"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Status</FormLabel>
              <Select onValueChange={field.onChange} value={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="ACTIVE">Active</SelectItem>
                  <SelectItem value="INACTIVE">Inactive</SelectItem>
                  <SelectItem value="PAUSED">Paused</SelectItem>
                  <SelectItem value="EXPIRED">Expired</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="title"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                Title <span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <div>
          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Description</FormLabel>
                <FormControl>
                  <Textarea {...field} className="min-h-[100px]" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </CardContent>
    </Card>
  );
};

const DiscountConfig = ({
  form,
}: {
  form: ReturnType<typeof useForm<EditCouponForm>>;
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Discount Configuration</CardTitle>
      </CardHeader>
      <CardContent className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <FormField
          control={form.control}
          name="discountTypeId"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                Discount Type <span className="text-red-500">*</span>
              </FormLabel>
              <Select
                onValueChange={field.onChange}
                value={field.value?.toString()}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="1">Percentage</SelectItem>
                  <SelectItem value="2">Fixed Amount</SelectItem>
                  <SelectItem value="3">Flat Price</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="discountValue"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                Discount Value <span className="text-red-500">*</span>
              </FormLabel>
              <div className="relative">
                <FormControl>
                  {form.watch("discountTypeId") === 1 ? (
                    <Input
                      type="number"
                      {...field}
                      onChange={(e) => field.onChange(Number(e.target.value))}
                      className="pr-8"
                    />
                  ) : (
                    <CurrencyInput
                      value={field.value}
                      onChange={field.onChange}
                      className="pr-8"
                    />
                  )}
                </FormControl>
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 text-sm">
                  {form.watch("discountTypeId") === 1 ? "%" : "VND"}
                </div>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="minOrderAmount"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Minimum Order Amount</FormLabel>
              <div className="relative">
                <FormControl>
                  <CurrencyInput
                    value={field.value}
                    onChange={field.onChange}
                    className="pr-12"
                  />
                </FormControl>
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 text-sm">
                  VND
                </div>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="maxDiscountAmount"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Maximum Discount Amount</FormLabel>
              <div className="relative">
                <FormControl>
                  <CurrencyInput
                    value={field.value}
                    onChange={field.onChange}
                    className="pr-12"
                    placeholder="No limit"
                  />
                </FormControl>
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 text-sm">
                  VND
                </div>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />
      </CardContent>
    </Card>
  );
};

const UsageConfig = ({
  form,
}: {
  form: ReturnType<typeof useForm<EditCouponForm>>;
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Usage Configuration</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <FormField
          control={form.control}
          name="usageMethod"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                Usage Method <span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <RadioGroup
                  onValueChange={field.onChange}
                  value={field.value}
                  className="mt-3"
                >
                  <div className="flex items-center space-x-3 p-3 border rounded-lg">
                    <RadioGroupItem value="MANUAL" id="manual" />
                    <div className="flex-1">
                      <Label htmlFor="manual" className="font-medium">
                        Manual Entry
                      </Label>
                      <p className="text-sm text-gray-600">
                        Users must enter the coupon code
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3 p-3 border rounded-lg">
                    <RadioGroupItem value="AUTO" id="auto" />
                    <div className="flex-1">
                      <Label htmlFor="auto" className="font-medium">
                        Auto-applied
                      </Label>
                      <p className="text-sm text-gray-600">
                        Automatically applied at checkout
                      </p>
                    </div>
                  </div>
                </RadioGroup>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="maxUsageCount"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Total Usage Limit</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    {...field}
                    onChange={(e) =>
                      field.onChange(
                        e.target.value ? Number(e.target.value) : undefined,
                      )
                    }
                    placeholder="Unlimited"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="maxUsagePerUser"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Per User Limit</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    {...field}
                    onChange={(e) =>
                      field.onChange(
                        e.target.value ? Number(e.target.value) : undefined,
                      )
                    }
                    placeholder="No limit"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </CardContent>
    </Card>
  );
};

const ValidityPeriod = ({
  form,
}: {
  form: ReturnType<typeof useForm<EditCouponForm>>;
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Validity Period</CardTitle>
      </CardHeader>
      <CardContent className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <FormField
          control={form.control}
          name="validFrom"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Valid From</FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant={"outline"}
                      className={cn(
                        "pl-3 text-left font-normal",
                        !field.value && "text-muted-foreground",
                      )}
                    >
                      {field.value ? (
                        format(field.value, "PPP")
                      ) : (
                        <span>Pick a date</span>
                      )}
                      <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={field.value}
                    onSelect={field.onChange}
                    autoFocus
                  />
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="validUntil"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Valid Until</FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant={"outline"}
                      className={cn(
                        "pl-3 text-left font-normal",
                        !field.value && "text-muted-foreground",
                      )}
                    >
                      {field.value ? (
                        format(field.value, "PPP")
                      ) : (
                        <span>Pick a date</span>
                      )}
                      <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={field.value}
                    onSelect={field.onChange}
                    autoFocus
                  />
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          )}
        />
      </CardContent>
    </Card>
  );
};

const TimeRestrictions = ({
  timeRestrictions,
  setTimeRestrictions,
  showTimeRestrictions,
  setShowTimeRestrictions,
}: {
  timeRestrictions: CouponTimeRestriction[];
  setTimeRestrictions: React.Dispatch<
    React.SetStateAction<CouponTimeRestriction[]>
  >;
  showTimeRestrictions: boolean;
  setShowTimeRestrictions: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  const dayNames = [
    "Sunday",
    "Monday",
    "Tuesday",
    "Wednesday",
    "Thursday",
    "Friday",
    "Saturday",
  ];

  const addTimeRestriction = (
    type: CouponTimeRestriction["restrictionType"],
  ) => {
    const newRestriction: CouponTimeRestriction = {
      id: 0,
      couponId: 0,
      restrictionType: type,
      ...(type === "DAYS_OF_WEEK" && { allowedDaysOfWeek: [] }),
      ...(type === "HOURS_OF_DAY" && {
        allowedHoursStart: 0,
        allowedHoursEnd: 23,
      }),
      ...(type === "SPECIFIC_DATES" && { specificDates: [] }),
      ...(type === "RECURRING_DATES" && { recurrencePattern: "MONTHLY" }),
    };
    setTimeRestrictions([...timeRestrictions, newRestriction]);
  };

  const updateTimeRestriction = (
    index: number,
    updates: Partial<CouponTimeRestriction>,
  ) => {
    const updated = [...timeRestrictions];
    updated[index] = { ...updated[index], ...updates };
    setTimeRestrictions(updated);
  };

  const removeTimeRestriction = (index: number) => {
    setTimeRestrictions(timeRestrictions.filter((_, i) => i !== index));
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          Time Restrictions
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={() => setShowTimeRestrictions(!showTimeRestrictions)}
          >
            {showTimeRestrictions ? "Hide" : "Show"} Time Restrictions
          </Button>
        </CardTitle>
      </CardHeader>
      {showTimeRestrictions && (
        <CardContent className="space-y-6">
          <div className="flex flex-wrap gap-2">
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => addTimeRestriction("DAYS_OF_WEEK")}
            >
              <Plus className="h-4 w-4 mr-2" /> Days of Week
            </Button>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => addTimeRestriction("HOURS_OF_DAY")}
            >
              <Plus className="h-4 w-4 mr-2" /> Hour Range
            </Button>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => addTimeRestriction("SPECIFIC_DATES")}
            >
              <Plus className="h-4 w-4 mr-2" /> Specific Dates
            </Button>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => addTimeRestriction("RECURRING_DATES")}
            >
              <Plus className="h-4 w-4 mr-2" /> Recurring Pattern
            </Button>
          </div>
          <div className="space-y-4">
            {timeRestrictions.map((restriction, index) => (
              <Card key={index} className="border-l-4 border-l-blue-500">
                <CardContent className="pt-4">
                  <div className="flex items-center justify-between mb-4">
                    <Badge variant="outline">
                      {restriction.restrictionType?.replace("_", " ")}
                    </Badge>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeTimeRestriction(index)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                  {restriction.restrictionType === "DAYS_OF_WEEK" && (
                    <div>
                      <Label className="text-sm font-medium mb-3 block">
                        Select Days
                      </Label>
                      <div className="grid grid-cols-4 gap-2">
                        {dayNames.map((day, dayIndex) => (
                          <div
                            key={day}
                            className="flex items-center space-x-2"
                          >
                            <Checkbox
                              id={`day-${index}-${dayIndex}`}
                              checked={
                                restriction.allowedDaysOfWeek?.includes(
                                  dayIndex,
                                ) || false
                              }
                              onCheckedChange={(checked) => {
                                const currentDays =
                                  restriction.allowedDaysOfWeek || [];
                                const newDays = checked
                                  ? [...currentDays, dayIndex]
                                  : currentDays.filter((d) => d !== dayIndex);
                                updateTimeRestriction(index, {
                                  allowedDaysOfWeek: newDays,
                                });
                              }}
                            />
                            <Label
                              htmlFor={`day-${index}-${dayIndex}`}
                              className="text-sm"
                            >
                              {day.slice(0, 3)}
                            </Label>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                  {restriction.restrictionType === "HOURS_OF_DAY" && (
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label className="text-sm font-medium">
                          Start Hour
                        </Label>
                        <Input
                          type="number"
                          min="0"
                          max="23"
                          value={restriction.allowedHoursStart || 0}
                          onChange={(e) =>
                            updateTimeRestriction(index, {
                              allowedHoursStart: Number(e.target.value),
                            })
                          }
                        />
                      </div>
                      <div>
                        <Label className="text-sm font-medium">End Hour</Label>
                        <Input
                          type="number"
                          min="0"
                          max="23"
                          value={restriction.allowedHoursEnd || 23}
                          onChange={(e) =>
                            updateTimeRestriction(index, {
                              allowedHoursEnd: Number(e.target.value),
                            })
                          }
                        />
                      </div>
                    </div>
                  )}
                  {restriction.restrictionType === "SPECIFIC_DATES" && (
                    <div>
                      <Label className="text-sm font-medium mb-3 block">
                        Specific Dates
                      </Label>
                      <div className="space-y-2">
                        {restriction.specificDates?.map((date, dateIndex) => (
                          <div
                            key={dateIndex}
                            className="flex items-center gap-2"
                          >
                            <Input
                              type="date"
                              value={date ? date.slice(0, 10) : ""}
                              onChange={(e) => {
                                const newDates = [
                                  ...(restriction.specificDates || []),
                                ];
                                newDates[dateIndex] = e.target.value;
                                updateTimeRestriction(index, {
                                  specificDates: newDates,
                                });
                              }}
                            />
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                const newDates =
                                  restriction.specificDates?.filter(
                                    (_, i) => i !== dateIndex,
                                  );
                                updateTimeRestriction(index, {
                                  specificDates: newDates,
                                });
                              }}
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                        ))}
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            const newDates = [
                              ...(restriction.specificDates || []),
                              "",
                            ];
                            updateTimeRestriction(index, {
                              specificDates: newDates,
                            });
                          }}
                        >
                          <Plus className="h-4 w-4 mr-2" /> Add Date
                        </Button>
                      </div>
                    </div>
                  )}
                  {restriction.restrictionType === "RECURRING_DATES" && (
                    <div className="space-y-4">
                      <div>
                        <Label className="text-sm font-medium">
                          Recurrence Pattern
                        </Label>
                        <Select
                          value={restriction.recurrencePattern || "MONTHLY"}
                          onValueChange={(value) =>
                            updateTimeRestriction(index, {
                              recurrencePattern: value as any,
                            })
                          }
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="DAILY">Daily</SelectItem>
                            <SelectItem value="WEEKLY">Weekly</SelectItem>
                            <SelectItem value="MONTHLY">Monthly</SelectItem>
                            <SelectItem value="QUARTERLY">Quarterly</SelectItem>
                            <SelectItem value="YEARLY">Yearly</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      {(restriction.recurrencePattern === "MONTHLY" ||
                        restriction.recurrencePattern === "YEARLY") && (
                        <div>
                          <Label className="text-sm font-medium">
                            Day of Month
                          </Label>
                          <Input
                            type="number"
                            min="1"
                            max="31"
                            value={restriction.recurrenceDayOfMonth || 1}
                            onChange={(e) =>
                              updateTimeRestriction(index, {
                                recurrenceDayOfMonth: Number(e.target.value),
                              })
                            }
                          />
                        </div>
                      )}
                      {restriction.recurrencePattern === "YEARLY" && (
                        <div>
                          <Label className="text-sm font-medium">Month</Label>
                          <Select
                            value={
                              restriction.recurrenceMonth?.toString() || "1"
                            }
                            onValueChange={(value) =>
                              updateTimeRestriction(index, {
                                recurrenceMonth: Number(value),
                              })
                            }
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="1">January</SelectItem>
                              <SelectItem value="2">February</SelectItem>
                              <SelectItem value="3">March</SelectItem>
                              <SelectItem value="4">April</SelectItem>
                              <SelectItem value="5">May</SelectItem>
                              <SelectItem value="6">June</SelectItem>
                              <SelectItem value="7">July</SelectItem>
                              <SelectItem value="8">August</SelectItem>
                              <SelectItem value="9">September</SelectItem>
                              <SelectItem value="10">October</SelectItem>
                              <SelectItem value="11">November</SelectItem>
                              <SelectItem value="12">December</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      )}
                      {restriction.recurrencePattern === "WEEKLY" && (
                        <div>
                          <Label className="text-sm font-medium">
                            Day of Week
                          </Label>
                          <Select
                            value={
                              restriction.recurrenceDayOfWeek?.toString() || "0"
                            }
                            onValueChange={(value) =>
                              updateTimeRestriction(index, {
                                recurrenceDayOfWeek: Number(value),
                              })
                            }
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {dayNames.map((day, dayIndex) => (
                                <SelectItem
                                  key={dayIndex}
                                  value={dayIndex.toString()}
                                >
                                  {day}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      )}
    </Card>
  );
};

const UserEligibility = ({
  userEligibility,
  setUserEligibility,
  showUserEligibility,
  setShowUserEligibility,
}: {
  userEligibility: CouponUserEligibility[];
  setUserEligibility: React.Dispatch<
    React.SetStateAction<CouponUserEligibility[]>
  >;
  showUserEligibility: boolean;
  setShowUserEligibility: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  const addUserEligibility = () => {
    const newEligibility: CouponUserEligibility = { id: 0, couponId: 0 };
    setUserEligibility([...userEligibility, newEligibility]);
  };

  const updateUserEligibility = (
    index: number,
    updates: Partial<CouponUserEligibility>,
  ) => {
    const updated = [...userEligibility];
    updated[index] = { ...updated[index], ...updates };
    setUserEligibility(updated);
  };

  const removeUserEligibility = (index: number) => {
    setUserEligibility(userEligibility.filter((_, i) => i !== index));
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          User Eligibility Rules
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={() => setShowUserEligibility(!showUserEligibility)}
          >
            {showUserEligibility ? "Hide" : "Show"} User Eligibility
          </Button>
        </CardTitle>
      </CardHeader>
      {showUserEligibility && (
        <CardContent className="space-y-6">
          <div className="flex justify-start">
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={addUserEligibility}
            >
              <Plus className="h-4 w-4 mr-2" /> Add Eligibility Rule
            </Button>
          </div>
          <div className="space-y-4">
            {userEligibility.map((eligibility, index) => (
              <Card key={index} className="border-l-4 border-l-green-500">
                <CardContent className="pt-4">
                  <div className="flex items-center justify-between mb-4">
                    <Badge variant="outline">User Eligibility Rule</Badge>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeUserEligibility(index)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm font-medium">User ID</Label>
                      <Input
                        placeholder="Specific user ID"
                        value={eligibility.userId || ""}
                        onChange={(e) =>
                          updateUserEligibility(index, {
                            userId: e.target.value
                              ? Number(e.target.value)
                              : undefined,
                          })
                        }
                      />
                    </div>
                    <div>
                      <Label className="text-sm font-medium">User Type</Label>
                      <Select
                        value={eligibility.userType || ""}
                        onValueChange={(value) =>
                          updateUserEligibility(index, {
                            userType: value || undefined,
                          })
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select user type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="NEW">New Users</SelectItem>
                          <SelectItem value="VIP">VIP Users</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">
                        Min Account Age (Days)
                      </Label>
                      <Input
                        type="number"
                        placeholder="No limit"
                        value={eligibility.minAccountAgeDays || ""}
                        onChange={(e) =>
                          updateUserEligibility(index, {
                            minAccountAgeDays: e.target.value
                              ? Number(e.target.value)
                              : undefined,
                          })
                        }
                      />
                    </div>
                    <div>
                      <Label className="text-sm font-medium">
                        Max Account Age (Days)
                      </Label>
                      <Input
                        type="number"
                        placeholder="No limit"
                        value={eligibility.maxAccountAgeDays || ""}
                        onChange={(e) =>
                          updateUserEligibility(index, {
                            maxAccountAgeDays: e.target.value
                              ? Number(e.target.value)
                              : undefined,
                          })
                        }
                      />
                    </div>
                    <div>
                      <Label className="text-sm font-medium">
                        Min Previous Orders
                      </Label>
                      <Input
                        type="number"
                        placeholder="No limit"
                        value={eligibility.minPreviousOrders || ""}
                        onChange={(e) =>
                          updateUserEligibility(index, {
                            minPreviousOrders: e.target.value
                              ? Number(e.target.value)
                              : undefined,
                          })
                        }
                      />
                    </div>
                    <div>
                      <Label className="text-sm font-medium">
                        Max Previous Orders
                      </Label>
                      <Input
                        type="number"
                        placeholder="No limit"
                        value={eligibility.maxPreviousOrders || ""}
                        onChange={(e) =>
                          updateUserEligibility(index, {
                            maxPreviousOrders: e.target.value
                              ? Number(e.target.value)
                              : undefined,
                          })
                        }
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      )}
    </Card>
  );
};

const ProductRestrictions = ({
  productRestrictions,
  setProductRestrictions,
  showProductRestrictions,
  setShowProductRestrictions,
}: {
  productRestrictions: CouponProductRestriction[];
  setProductRestrictions: React.Dispatch<
    React.SetStateAction<CouponProductRestriction[]>
  >;
  showProductRestrictions: boolean;
  setShowProductRestrictions: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  const addProductRestriction = () => {
    const newRestriction: CouponProductRestriction = {
      id: 0,
      couponId: 0,
      isIncluded: true,
      productName: "",
      categoryName: "",
    };
    setProductRestrictions([...productRestrictions, newRestriction]);
  };

  const updateProductRestriction = (
    index: number,
    updates: Partial<CouponProductRestriction>,
  ) => {
    const updated = [...productRestrictions];
    updated[index] = { ...updated[index], ...updates };
    setProductRestrictions(updated);
  };

  const removeProductRestriction = (index: number) => {
    setProductRestrictions(productRestrictions.filter((_, i) => i !== index));
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          Product & Category Restrictions
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={() => setShowProductRestrictions(!showProductRestrictions)}
          >
            {showProductRestrictions ? "Hide" : "Show"} Product Restrictions
          </Button>
        </CardTitle>
      </CardHeader>
      {showProductRestrictions && (
        <CardContent className="space-y-6">
          <div className="flex justify-start">
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={addProductRestriction}
            >
              <Plus className="h-4 w-4 mr-2" /> Add Product Restriction
            </Button>
          </div>
          <div className="space-y-4">
            {productRestrictions.map((restriction, index) => (
              <Card key={index} className="border-l-4 border-l-purple-500">
                <CardContent className="pt-4">
                  <div className="flex items-center justify-between mb-4">
                    <Badge variant="outline">Product Restriction</Badge>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeProductRestriction(index)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <Label className="text-sm font-medium">
                        Restriction Type
                      </Label>
                      <Select
                        value={restriction.isIncluded ? "included" : "excluded"}
                        onValueChange={(value) =>
                          updateProductRestriction(index, {
                            isIncluded: value === "included",
                          })
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="included">Include Only</SelectItem>
                          <SelectItem value="excluded">Exclude</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">
                        Product Name
                      </Label>
                      <Input
                        placeholder="No limit"
                        value={restriction.productName || ""}
                        onChange={(e) =>
                          updateProductRestriction(index, {
                            productName: e.target.value || undefined,
                          })
                        }
                      />
                    </div>
                    <div>
                      <Label className="text-sm font-medium">
                        Category Name
                      </Label>
                      <Input
                        placeholder="No limit"
                        value={restriction.categoryName || ""}
                        onChange={(e) =>
                          updateProductRestriction(index, {
                            categoryName: e.target.value || undefined,
                          })
                        }
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      )}
    </Card>
  );
};

export default function EditCoupon() {
  const { id } = useParams({ from: "/coupons/$id/edit" });
  const navigate = useNavigate();
  const { setActiveTab } = useContext(LayoutContext);
  const {
    data: rawCoupon,
    isPending,
    isLoading,
    isError,
  } = useCoupon(Number(id));
  const coupon = rawCoupon as CouponDetail | undefined;
  const updateCoupon = useUpdateCoupon();

  useEffect(() => {
    setActiveTab("edit");
  }, [setActiveTab]);

  const [timeRestrictions, setTimeRestrictions] = useState<
    CouponTimeRestriction[]
  >(coupon?.timeRestrictions || []);
  const [showTimeRestrictions, setShowTimeRestrictions] = useState(
    Boolean(coupon?.timeRestrictions?.length),
  );

  const [userEligibility, setUserEligibility] = useState<
    CouponUserEligibility[]
  >(coupon?.userEligibility || []);
  const [showUserEligibility, setShowUserEligibility] = useState(
    Boolean(coupon?.userEligibility?.length),
  );

  const [productRestrictions, setProductRestrictions] = useState<
    CouponProductRestriction[]
  >(coupon?.productRestrictions || []);
  const [showProductRestrictions, setShowProductRestrictions] = useState(
    Boolean(coupon?.productRestrictions?.length),
  );

  const form = useForm<EditCouponForm>({
    resolver: zodResolver(editCouponSchema),
    defaultValues: {
      title: coupon?.title || "",
      description: coupon?.description || "",
      discountTypeId: coupon?.discountTypeId,
      discountValue: coupon?.discountValue || 0,
      usageMethod: coupon?.usageMethod,
      validFrom: coupon ? new Date(coupon.validFrom ?? new Date()) : new Date(),
      validUntil: coupon
        ? new Date(coupon.validUntil ?? new Date())
        : new Date(),
      maxUsageCount: coupon?.maxUsageCount,
      maxUsagePerUser: coupon?.maxUsagePerUser,
      minOrderAmount: coupon?.minOrderAmount || 0,
      maxDiscountAmount: coupon?.maxDiscountAmount,
      status: (coupon?.status as any) || "ACTIVE",
    },
  });

  useEffect(() => {
    if (coupon) {
      form.reset({
        title: coupon.title || "",
        description: coupon.description || "",
        discountTypeId: coupon.discountTypeId,
        discountValue: coupon.discountValue || 0,
        usageMethod: coupon.usageMethod,
        validFrom: new Date(coupon.validFrom ?? new Date()),
        validUntil: new Date(coupon.validUntil ?? new Date()),
        maxUsageCount: coupon.maxUsageCount,
        maxUsagePerUser: coupon.maxUsagePerUser,
        minOrderAmount: coupon.minOrderAmount || 0,
        maxDiscountAmount: coupon.maxDiscountAmount,
        status: coupon.status as any,
      });
      setTimeRestrictions(coupon.timeRestrictions || []);
      setShowTimeRestrictions(Boolean(coupon.timeRestrictions?.length));
      setUserEligibility(coupon.userEligibility || []);
      setShowUserEligibility(Boolean(coupon.userEligibility?.length));
      setProductRestrictions(coupon.productRestrictions || []);
      setShowProductRestrictions(Boolean(coupon.productRestrictions?.length));
    }
  }, [coupon, form]);

  const onSubmit = async (data: EditCouponForm) => {
    const payload: UpdateCouponRequest = {
      ...data,
      validFrom: data.validFrom.toISOString(),
      validUntil: data.validUntil.toISOString(),
      timeRestrictions,
      userEligibility,
      productRestrictions,
    };

    await updateCoupon.mutateAsync({
      params: { path: { id: Number(id) } },
      body: payload,
    });
    setActiveTab("detail");
    navigate({ to: `/coupons/${id}` });
  };

  const handleCancel = () => {
    setActiveTab("detail");
    navigate({ to: `/coupons/${id}` });
  };

  // if (isLoading) {
  //   return (
  //     <div className="flex items-center justify-center min-h-screen bg-gray-50">
  //       Loading...
  //     </div>
  //   );
  // }

  if (isError || (!isPending && !isLoading && !coupon)) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            Coupon Not Found
          </h1>
          <Button
            onClick={() => {
              setActiveTab("coupons");
              navigate({ to: "/coupons" });
            }}
          >
            Back to Coupons
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-6xl mx-auto px-8 py-8">
        <div className="flex items-center justify-between mb-8 bg-white p-6 rounded-lg shadow-sm">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">
              {coupon?.couponCode}
            </h2>
            <p className="text-gray-600">{coupon?.title}</p>
          </div>
          <div className="flex items-center gap-4">
            <Badge className="bg-green-100 text-green-800 px-3 py-1">
              {coupon?.status}
            </Badge>
            <span className="text-gray-600">Last updated: 2 hours ago</span>
          </div>
        </div>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
            {updateCoupon.isError && (
              <div className="text-sm text-destructive">
                {(updateCoupon.error as ResponseError)?.message ||
                  (updateCoupon.error as Error).message}
              </div>
            )}
            <BasicInfo coupon={coupon} form={form} />
            <DiscountConfig form={form} />
            <UsageConfig form={form} />
            <ValidityPeriod form={form} />
            <TimeRestrictions
              timeRestrictions={timeRestrictions}
              setTimeRestrictions={setTimeRestrictions}
              showTimeRestrictions={showTimeRestrictions}
              setShowTimeRestrictions={setShowTimeRestrictions}
            />
            <UserEligibility
              userEligibility={userEligibility}
              setUserEligibility={setUserEligibility}
              showUserEligibility={showUserEligibility}
              setShowUserEligibility={setShowUserEligibility}
            />
            <ProductRestrictions
              productRestrictions={productRestrictions}
              setProductRestrictions={setProductRestrictions}
              showProductRestrictions={showProductRestrictions}
              setShowProductRestrictions={setShowProductRestrictions}
            />
            <div className="flex flex-wrap gap-4 justify-between">
              <div className="flex gap-4">
                <Button
                  type="button"
                  variant="outline"
                  className="border-red-500 text-red-600 hover:bg-red-50"
                >
                  🗑️ Delete Coupon
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  className="border-orange-500 text-orange-600 hover:bg-orange-50"
                >
                  ⏸️
                  {form.watch("status") === "ACTIVE"
                    ? "Deactivate"
                    : "Activate"}
                </Button>
              </div>
              <div className="flex gap-4">
                <Button type="button" variant="outline" onClick={handleCancel}>
                  Cancel
                </Button>
                <Button type="submit" className="bg-blue-600 hover:bg-blue-700">
                  💾 Save Changes
                </Button>
              </div>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
}
