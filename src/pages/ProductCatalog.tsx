import { useState, useMemo, useEffect } from "react";
import { useProductsList } from "@/services/products";
import {
  useEligibleAutoCoupons,
  useCheckCouponEligibility,
} from "@/services/vouchers";
import { useUser } from "@/services/user";
import { useCreateOrder } from "@/services/orders";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Search,
  ShoppingCart,
  Minus,
  Plus,
  Check,
  Gift,
  Ticket,
  ChevronLeft,
  ChevronRight,
  Receipt,
} from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import type { Product } from "@/types/products";
import {
  getCoreRowModel,
  getPaginationRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { Skeleton } from "@/components/ui/skeleton";
import { createProductListColumns } from "@/components/products/ProductListColumns";
import type {
  CheckEligibilityResponse,
  AutoEligibilityRequest,
  EligibleAutoCoupon,
  CheckEligibilityRequest,
} from "@/types/coupons";

interface DisplayCoupon {
  code: string;
  description: string;
  status: "available" | "not-available";
  isAutoApplied: boolean;
  isApplied: boolean;
  badge: string;
  savings: number;
  icon: string;
  bgColor: string;
  textColor: string;
}

const getCouponStyle = (
  type: string,
): { icon: string; bgColor: string; textColor: string } => {
  const key = type.replace(/\s+/g, "_").toUpperCase();
  switch (key) {
    case "PERCENT":
      return {
        icon: "%",
        bgColor: "bg-green-50",
        textColor: "text-green-600",
      };
    case "FIXED":
      return {
        icon: "gift",
        bgColor: "bg-blue-50",
        textColor: "text-blue-600",
      };
    case "FLAT":
      return {
        icon: "star",
        bgColor: "bg-purple-50",
        textColor: "text-purple-600",
      };
    default:
      return {
        icon: "gift",
        bgColor: "bg-blue-50",
        textColor: "text-blue-600",
      };
  }
};

const manualCouponStyle = {
  icon: "ticket",
  bgColor: "bg-yellow-50",
  textColor: "text-yellow-600",
};

function getPageNumbers(current: number, total: number) {
  if (total <= 5) {
    return Array.from({ length: total }, (_, i) => i + 1);
  }
  const pages: (number | string)[] = [1];
  let start = current - 1;
  let end = current + 1;

  if (start <= 2) {
    start = 2;
    end = start + 2;
  }

  if (end >= total - 1) {
    end = total - 1;
    start = end - 2;
  }

  if (start > 2) pages.push("...");
  for (let i = start; i <= end; i++) pages.push(i);
  if (end < total - 1) pages.push("...");
  pages.push(total);
  return pages;
}

const ProductCatalog = () => {
  const [cartItems, setCartItems] = useState<
    (Product & { quantity: number })[]
  >([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [appliedCoupon, setAppliedCoupon] = useState<string>("");
  const [couponInput, setCouponInput] = useState("");
  const [manualDiscount, setManualDiscount] = useState(0);
  const [couponMessage, setCouponMessage] = useState<string | null>(null);
  const [manualCoupon, setManualCoupon] = useState<DisplayCoupon | null>(null);
  const [pagination, setPagination] = useState({ pageIndex: 0, pageSize: 9 });
  const [showSuccess, setShowSuccess] = useState(false);

  const createOrder = useCreateOrder();

  const { data, isLoading, isFetching, error } = useProductsList({
    page: pagination.pageIndex + 1,
    limit: pagination.pageSize,
  });
  const products = data?.data ?? [];
  const totalPages = data?.totalPages ?? 1;

  const user = useUser();
  const eligibleCoupons = useEligibleAutoCoupons();
  const checkEligibility = useCheckCouponEligibility();

  const rawCoupons: EligibleAutoCoupon[] =
    (eligibleCoupons.data as EligibleAutoCoupon[]) ?? [];

  useEffect(() => {
    if (manualCoupon) return;
    if (rawCoupons.length > 0) {
      const firstCoupon = rawCoupons[0]?.coupon;
      if (firstCoupon) {
        setAppliedCoupon(firstCoupon.couponCode ?? "");
        setManualDiscount(0);
        setCouponMessage(null);
      }
    } else {
      setAppliedCoupon("");
      setManualDiscount(0);
    }
  }, [eligibleCoupons.data, manualCoupon]);

  useEffect(() => {
    setManualCoupon((prev) => {
      if (!prev) return prev;
      const shouldApply = prev.code === appliedCoupon;
      if (prev.isApplied === shouldApply) return prev;
      return { ...prev, isApplied: shouldApply };
    });
  }, [appliedCoupon]);

  const bestCouponCode = rawCoupons[0]?.coupon?.couponCode ?? "";

  const autoCoupons: DisplayCoupon[] = rawCoupons.map((c) => {
    const coupon = (c.coupon ?? {}) as NonNullable<
      EligibleAutoCoupon["coupon"]
    >;
    const style = getCouponStyle(coupon.discountType?.typeName ?? "");
    const isApplied = coupon.couponCode === appliedCoupon;
    const isAutoApplied =
      coupon.couponCode === bestCouponCode &&
      coupon.couponCode === appliedCoupon;
    const status = coupon.status === "ACTIVE" ? "available" : "not-available";

    return {
      code: coupon.couponCode ?? "",
      description: coupon.title ?? "",
      status,
      isAutoApplied,
      isApplied,
      badge: isAutoApplied
        ? "Best Match"
        : status === "available"
          ? "Available"
          : "Not Eligible",
      savings:
        c.discountAmount !== undefined
          ? c.discountAmount
          : (coupon.discountValue ?? 0),
      icon: style.icon,
      bgColor: style.bgColor,
      textColor: style.textColor,
    };
  });

  const availableCoupons: DisplayCoupon[] = manualCoupon
    ? [manualCoupon, ...autoCoupons.filter((c) => c.code !== manualCoupon.code)]
    : autoCoupons;

  const addToCart = (product: Product) => {
    const existingItem = cartItems.find((item) => item.id === product.id);
    if (existingItem) {
      setCartItems(
        cartItems.map((item) =>
          item.id === product.id
            ? { ...item, quantity: item.quantity + 1 }
            : item,
        ),
      );
    } else {
      setCartItems([...cartItems, { ...product, quantity: 1 }]);
    }
  };

  const updateQuantity = (id: number, change: number) => {
    setCartItems(
      cartItems
        .map((item) => {
          if (item.id === id) {
            const newQuantity = item.quantity + change;
            return newQuantity > 0 ? { ...item, quantity: newQuantity } : null;
          }
          return item;
        })
        .filter(Boolean) as (Product & { quantity: number })[],
    );
  };

  const removeFromCart = (id: number) => {
    setCartItems(cartItems.filter((item) => item.id !== id));
  };

  const columns = useMemo(
    () => createProductListColumns(addToCart),
    [addToCart],
  );
  const table = useReactTable({
    data: products.filter((p) =>
      p.name.toLowerCase().includes(searchTerm.toLowerCase()),
    ),
    columns,
    pageCount: totalPages,
    manualPagination: true,
    state: { pagination },
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  });
  const pageNumbers = useMemo(
    () => getPageNumbers(pagination.pageIndex + 1, totalPages),
    [pagination.pageIndex, totalPages],
  );
  const isInitialLoading = isLoading && !data;

  const subtotal = cartItems.reduce(
    (sum, item) => sum + item.price * item.quantity,
    0,
  );

  useEffect(() => {
    if (!user.data?.id || cartItems.length === 0) return;
    const body: AutoEligibilityRequest = {
      userId: user.data.id,
      orderAmount: subtotal,
      orderTimestamp: new Date().toISOString(),
      cartItems: cartItems.map((item) => ({
        productId: item.id,
        ...(item.categoryId !== undefined && {
          categoryId: item.categoryId,
        }),
        price: item.price,
        quantity: item.quantity,
      })),
    };
    eligibleCoupons.mutate({
      body: body,
    });
  }, [cartItems, subtotal, user.data]);

  const appliedCouponInfo =
    rawCoupons.find((c) => c.coupon?.couponCode === appliedCoupon) ||
    (manualCoupon && manualCoupon.code === appliedCoupon
      ? {
          discountAmount: manualDiscount,
          coupon: { discountValue: manualDiscount },
        }
      : undefined);
  const discount =
    appliedCouponInfo?.discountAmount ??
    appliedCouponInfo?.coupon?.discountValue ??
    0;
  const total = subtotal - discount;

  const applyCoupon = async () => {
    if (!couponInput || !user.data?.id || cartItems.length === 0) return;
    try {
      const body: CheckEligibilityRequest = {
        userId: user.data.id,
        couponCode: couponInput,
        orderAmount: subtotal,
        orderTimestamp: new Date().toISOString(),
        cartItems: cartItems.map((item) => ({
          productId: item.id,
          ...(item.categoryId !== undefined && { categoryId: item.categoryId }),
          price: item.price,
          quantity: item.quantity,
        })),
      };
      const res = (await checkEligibility.mutateAsync({
        body: body,
      })) as CheckEligibilityResponse;

      if (res.eligible) {
        const coupon = (res.coupon ?? {}) as NonNullable<
          CheckEligibilityResponse["coupon"]
        >;
        const manual: DisplayCoupon = {
          code: coupon.couponCode ?? couponInput,
          description: coupon.title ?? "",
          status: "available",
          isAutoApplied: false,
          isApplied: true,
          badge: "Manual",
          savings:
            res.discountAmount !== undefined
              ? res.discountAmount
              : (coupon.discountValue ?? 0),
          icon: manualCouponStyle.icon,
          bgColor: manualCouponStyle.bgColor,
          textColor: manualCouponStyle.textColor,
        };
        setManualCoupon(manual);
        setAppliedCoupon(coupon.couponCode ?? couponInput);
        setManualDiscount(res.discountAmount ?? 0);
        setCouponMessage(null);
      } else {
        setCouponMessage((res as any).message ?? "Coupon not eligible");
      }
    } catch (err) {
      setCouponMessage(
        (err as ResponseError)?.message ?? (err as Error).message,
      );
    }
    setCouponInput("");
  };

  const handleCheckout = async () => {
    if (!user.data?.id || cartItems.length === 0) return;
    try {
      await createOrder.mutateAsync({
        body: {
          userId: user.data.id,
          couponCode: appliedCoupon || undefined,
          orderAmount: subtotal,
          orderTimestamp: new Date().toISOString(),
          items: cartItems.map((item) => ({
            productId: item.id,
            quantity: item.quantity,
          })),
        },
      });
      setShowSuccess(true);
      setCartItems([]);
    } catch (err) {
      console.error(err);
    }
  };

  const getCouponIcon = (iconType: string) => {
    switch (iconType) {
      case "%":
        return (
          <div className="w-6 h-6 rounded-full bg-green-100 flex items-center justify-center text-green-600 text-sm font-bold">
            %
          </div>
        );
      case "gift":
        return <Gift className="w-6 h-6 text-blue-600" />;
      case "star":
        return (
          <div className="w-6 h-6 rounded-full bg-purple-100 flex items-center justify-center text-purple-600 text-sm">
            ★
          </div>
        );
      case "clock":
        return (
          <div className="w-6 h-6 rounded-full bg-gray-100 flex items-center justify-center text-gray-400 text-sm">
            ○
          </div>
        );
      case "ticket":
        return <Ticket className="w-6 h-6 text-yellow-600" />;
      default:
        return <Receipt className="w-6 h-6" />;
    }
  };

  if (error) {
    const message =
      (error as ResponseError)?.message || (error as Error).message;
    return <div className="p-6 text-red-600">{message}</div>;
  }

  if (isLoading && products.length === 0) {
    return <div className="p-6">Loading...</div>;
  }

  return (
    <>
      <div className="min-h-screen bg-gray-50">
        <div className="mx-auto px-4 py-8">
          <div className="grid grid-cols-3 gap-8">
            <div className="col-span-2">
              <div className="mb-6">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">
                  Product Catalog
                </h2>
                <div className="flex gap-4 mb-6">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Search products..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                  <Select defaultValue="all">
                    <SelectTrigger className="w-48">
                      <SelectValue placeholder="All Categories" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Categories</SelectItem>
                      <SelectItem value="office">Office Supplies</SelectItem>
                      <SelectItem value="electronics">Electronics</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="mb-8 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {isInitialLoading || isFetching
                  ? Array.from({ length: pagination.pageSize }).map(
                      (_, idx) => (
                        <Card
                          key={idx}
                          className="hover:shadow-lg transition-shadow"
                        >
                          <Skeleton className="w-full h-48 rounded-t-lg" />
                          <CardContent className="p-4 space-y-2">
                            <Skeleton className="h-6 w-3/4" />
                            <Skeleton className="h-4 w-1/2" />
                            <Skeleton className="h-4 w-full" />
                            <Skeleton className="h-9 w-1/2" />
                          </CardContent>
                        </Card>
                      ),
                    )
                  : table.getRowModel().rows.map((row) => {
                      const product = row.original;
                      return (
                        <Card
                          key={product.id}
                          className="hover:shadow-lg transition-shadow"
                        >
                          <div className="relative">
                            <img
                              src={product.imageUrl}
                              alt={product.name}
                              className="w-full h-48 object-cover rounded-t-lg"
                            />
                            {product.stockQuantity &&
                              product.stockQuantity > 0 && (
                                <Badge className="absolute top-2 left-2 bg-green-500 text-white">
                                  In Stock
                                </Badge>
                              )}
                          </div>
                          <CardContent className="p-4">
                            <h3 className="font-semibold text-lg mb-1">
                              {product.name}
                            </h3>
                            <p className="text-2xl font-bold text-blue-600 mb-2">
                              ₫{product.price.toLocaleString()}
                            </p>
                            <p className="text-gray-600 text-sm mb-2">
                              {product.description}
                            </p>
                            <p className="text-gray-500 text-sm mb-4">
                              Brand: {product.brand}
                            </p>
                            <Button
                              onClick={() => addToCart(product)}
                              className="w-full bg-blue-500 hover:bg-blue-600"
                            >
                              Add to Cart
                            </Button>
                          </CardContent>
                        </Card>
                      );
                    })}
              </div>

              <div className="flex flex-col items-center">
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="px-3 py-1"
                    onClick={() => table.previousPage()}
                    disabled={!table.getCanPreviousPage()}
                  >
                    <ChevronLeft className="w-4 h-4" /> Previous
                  </Button>
                  {pageNumbers.map((p, idx) =>
                    typeof p === "number" ? (
                      <Button
                        key={p}
                        variant={
                          p - 1 === table.getState().pagination.pageIndex
                            ? "default"
                            : "outline"
                        }
                        size="sm"
                        className="px-3 py-1"
                        onClick={() => table.setPageIndex(p - 1)}
                      >
                        {p}
                      </Button>
                    ) : (
                      <span key={`ellipsis-${idx}`} className="px-2">
                        ...
                      </span>
                    ),
                  )}
                  <Button
                    variant="outline"
                    size="sm"
                    className="px-3 py-1"
                    onClick={() => table.nextPage()}
                    disabled={!table.getCanNextPage()}
                  >
                    Next <ChevronRight className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>

            <div className="col-span-1">
              <Card className="py-0">
                <div className="p-4 border-b">
                  <h3 className="text-lg font-semibold flex items-center">
                    <ShoppingCart className="h-5 w-5 mr-2" />
                    Shopping Cart
                  </h3>
                </div>

                <div className="p-4 space-y-4">
                  {cartItems.map((item) => (
                    <div key={item.id} className="flex items-center space-x-3">
                      <img
                        src={item.imageUrl}
                        alt={item.name}
                        className="w-12 h-12 object-cover rounded"
                      />
                      <div className="flex-1">
                        <h4 className="font-medium text-sm">{item.name}</h4>
                        <p className="text-xs text-gray-500">
                          Brand: {item.brand}
                        </p>
                        <div className="flex items-center space-x-2 mt-1">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => updateQuantity(item.id, -1)}
                            className="h-6 w-6 p-0"
                          >
                            <Minus className="h-3 w-3" />
                          </Button>
                          <span className="text-sm">{item.quantity}</span>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => updateQuantity(item.id, 1)}
                            className="h-6 w-6 p-0"
                          >
                            <Plus className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold">
                          ₫{item.price.toLocaleString()}
                        </p>
                        <button
                          onClick={() => removeFromCart(item.id)}
                          className="text-xs text-red-500 hover:text-red-700"
                        >
                          Remove
                        </button>
                      </div>
                    </div>
                  ))}
                </div>

                <div className="p-4 border-t">
                  <h4 className="font-semibold mb-3">Order Summary</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Subtotal</span>
                      <span>₫{subtotal.toLocaleString()}</span>
                    </div>
                    {discount > 0 && (
                      <div className="flex justify-between text-green-600">
                        <span>Discount</span>
                        <span>-₫{discount.toLocaleString()}</span>
                      </div>
                    )}
                    <div className="flex justify-between font-semibold text-lg border-t pt-2">
                      <span>Total</span>
                      <span>₫{total.toLocaleString()}</span>
                    </div>
                  </div>
                </div>

                <div className="p-4 border-t">
                  <h4 className="font-semibold mb-4">Apply Coupon</h4>

                  {appliedCoupon && (
                    <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                      <div className="flex items-center space-x-2">
                        <Check className="h-4 w-4 text-green-600" />
                        <span className="text-sm font-medium text-green-800">
                          Coupon "{appliedCoupon}" applied successfully!
                          {discount > 0
                            ? ` Saves ₫${discount.toLocaleString()}`
                            : ""}
                          .
                        </span>
                      </div>
                    </div>
                  )}

                  <div className="mb-6">
                    <h4 className="text-sm font-medium text-gray-700 mb-3">
                      Enter coupon code
                    </h4>
                    <div className="flex gap-3 mb-2">
                      <div className="flex-1 relative">
                        <Receipt className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                        <Input
                          placeholder="Enter coupon code"
                          value={couponInput}
                          onChange={(e) => setCouponInput(e.target.value)}
                          className="bg-white pl-10"
                        />
                      </div>
                      <Button
                        onClick={applyCoupon}
                        className="px-6 bg-blue-600 hover:bg-blue-700"
                      >
                        Apply
                      </Button>
                    </div>
                    {couponMessage && (
                      <p className="text-sm text-red-500 mb-4">
                        {couponMessage}
                      </p>
                    )}
                    <div className="space-y-3">
                      {availableCoupons.map((coupon: DisplayCoupon, idx) => (
                        <div
                          key={`${coupon.code}-${idx}`}
                          className={`p-4 rounded-lg border ${coupon.bgColor}`}
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              {getCouponIcon(coupon.icon)}
                              <div>
                                <div className="flex items-center gap-2">
                                  <span className="font-semibold text-gray-900">
                                    {coupon.code}
                                  </span>
                                  <span
                                    className={`px-2 py-1 rounded text-xs font-medium ${
                                      coupon.isAutoApplied
                                        ? "bg-green-100 text-green-700"
                                        : coupon.status === "available"
                                          ? "bg-blue-100 text-blue-700"
                                          : "bg-gray-100 text-gray-500"
                                    }`}
                                  >
                                    {coupon.badge}
                                  </span>
                                </div>
                                <p className="text-sm text-gray-600 mt-1">
                                  {coupon.description}
                                </p>
                                {coupon.savings > 0 && (
                                  <p className="text-sm font-medium text-green-600 mt-1">
                                    Saves ₫{coupon.savings.toLocaleString()}
                                  </p>
                                )}
                              </div>
                            </div>
                            <div>
                              {coupon.isAutoApplied ? (
                                <div className="flex items-center text-green-600">
                                  <Check className="h-4 w-4 mr-1" />
                                  <span className="text-sm font-medium">
                                    Auto-Applied
                                  </span>
                                </div>
                              ) : coupon.status === "available" ? (
                                coupon.isApplied ? (
                                  <div className="flex items-center text-green-600">
                                    <Check className="h-4 w-4 mr-1" />
                                    <span className="text-sm font-medium">
                                      Applied
                                    </span>
                                  </div>
                                ) : (
                                  <Button
                                    onClick={() => {
                                      setAppliedCoupon(coupon.code);
                                      setManualDiscount(
                                        manualCoupon &&
                                          manualCoupon.code === coupon.code
                                          ? manualCoupon.savings
                                          : 0,
                                      );
                                      setCouponMessage(null);
                                    }}
                                    className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2"
                                  >
                                    Apply
                                  </Button>
                                )
                              ) : (
                                <span className="text-sm text-gray-400 font-medium">
                                  Not Available
                                </span>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                <div className="p-4">
                  <Button
                    className="w-full bg-blue-500 hover:bg-blue-600 text-white py-3"
                    onClick={handleCheckout}
                  >
                    Proceed to Checkout
                  </Button>
                </div>
              </Card>
            </div>
          </div>
        </div>
      </div>
      <Dialog open={showSuccess} onOpenChange={setShowSuccess}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Order Created</DialogTitle>
          </DialogHeader>
          <div className="text-sm">
            Your order has been placed successfully.
          </div>
          <DialogFooter>
            <Button onClick={() => setShowSuccess(false)}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default ProductCatalog;
