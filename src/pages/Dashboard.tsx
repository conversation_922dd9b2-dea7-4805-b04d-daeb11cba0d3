import { useEffect, useContext } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Link } from "@tanstack/react-router";
import { Ticket, ShoppingCart, Users, TrendingUp } from "lucide-react";
import { LayoutContext } from "@/routes/__root";

const Dashboard = () => {
  const { setActiveTab } = useContext(LayoutContext);
  useEffect(() => {
    setActiveTab("dashboard");
  }, [setActiveTab]);

  return (
    <div className="min-h-full bg-gradient-to-br from-purple-50 to-blue-50">
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            Coupon & Order Management System
          </h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-12">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Active Coupons
              </CardTitle>
              <Ticket className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">24</div>
              <p className="text-xs text-muted-foreground">
                +12% from last month
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Orders
              </CardTitle>
              <ShoppingCart className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">1,234</div>
              <p className="text-xs text-muted-foreground">
                +8% from last month
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Discount Given
              </CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">₫12.5M</div>
              <p className="text-xs text-muted-foreground">
                +15% from last month
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Active Users
              </CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">573</div>
              <p className="text-xs text-muted-foreground">
                +2% from last month
              </p>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Ticket className="h-5 w-5 text-purple-600" />
                <span>Coupon Management</span>
              </CardTitle>
              <CardDescription>
                Create, edit, and manage discount coupons for your customers
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-wrap gap-2">
                <Badge variant="secondary">Percentage Discount</Badge>
                <Badge variant="secondary">Fixed Amount</Badge>
                <Badge variant="secondary">Auto Apply</Badge>
              </div>
              <div className="flex space-x-2">
                <Button asChild className="flex-1">
                  <Link to="/coupons">View All Coupons</Link>
                </Button>
                <Button asChild variant="outline" className="flex-1">
                  <Link to="/coupons/new">Create New</Link>
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <ShoppingCart className="h-5 w-5 text-green-600" />
                <span>Order Processing</span>
              </CardTitle>
              <CardDescription>
                Process orders and calculate discounts automatically
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-wrap gap-2">
                <Badge variant="secondary">Real-time Calculation</Badge>
                <Badge variant="secondary">Auto Discount</Badge>
                <Badge variant="secondary">Order History</Badge>
              </div>
              <div className="flex space-x-2">
                <Button asChild className="flex-1" variant="secondary">
                  <Link to="/orders">View Orders</Link>
                </Button>
                <Button asChild variant="outline" className="flex-1">
                  <Link to="/orders/create">Order Calculator</Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
};

export default Dashboard;
