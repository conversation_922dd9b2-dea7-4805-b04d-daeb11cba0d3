/**
 * This file was auto-generated by openapi-typescript.
 * Do not make direct changes to the file.
 */

export interface paths {
    "/api/categories": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GET_gitlab_zalopay_vn/phunn4/coupon-api-gateway/internal/handlers_(*ProductHandler)_HandleListCategories-fm"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/login": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["POST_gitlab_zalopay_vn/phunn4/coupon-api-gateway/internal/handlers_(*AuthHandler)_HandleLogin-fm"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/logout": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["POST_gitlab_zalopay_vn/phunn4/coupon-api-gateway/internal/handlers_(*AuthHandler)_HandleLogout-fm"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/orders": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GET_gitlab_zalopay_vn/phunn4/coupon-api-gateway/internal/handlers_(*OrderHandler)_HandleListOrders-fm"];
        put?: never;
        post: operations["POST_gitlab_zalopay_vn/phunn4/coupon-api-gateway/internal/handlers_(*OrderHandler)_HandleCreateOrder-fm"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/orders/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GET_gitlab_zalopay_vn/phunn4/coupon-api-gateway/internal/handlers_(*OrderHandler)_HandleGetOrder-fm"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/orders/{id}/status": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put: operations["PUT_gitlab_zalopay_vn/phunn4/coupon-api-gateway/internal/handlers_(*OrderHandler)_HandleUpdateOrderStatus-fm"];
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/products": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GET_gitlab_zalopay_vn/phunn4/coupon-api-gateway/internal/handlers_(*ProductHandler)_HandleListProducts-fm"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/products/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GET_gitlab_zalopay_vn/phunn4/coupon-api-gateway/internal/handlers_(*ProductHandler)_HandleGetProduct-fm"];
        put: operations["PUT_gitlab_zalopay_vn/phunn4/coupon-api-gateway/internal/handlers_(*ProductHandler)_HandleUpdateProduct-fm"];
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/register": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["POST_gitlab_zalopay_vn/phunn4/coupon-api-gateway/internal/handlers_(*AuthHandler)_HandleRegister-fm"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/users/me": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GET_gitlab_zalopay_vn/phunn4/coupon-api-gateway/internal/handlers_(*UserHandler)_HandleGetMe-fm"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/users/me/orders/count": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GET_gitlab_zalopay_vn/phunn4/coupon-api-gateway/internal/handlers_(*OrderHandler)_HandleGetUserOrderCount-fm"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/users/me/vouchers/{voucher_id}/usage-count": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GET_gitlab_zalopay_vn/phunn4/coupon-api-gateway/internal/handlers_(*OrderHandler)_HandleGetUserVoucherUsageCount-fm"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/vouchers": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GET_gitlab_zalopay_vn/phunn4/coupon-api-gateway/internal/handlers_(*VoucherHandler)_HandleListVouchers-fm"];
        put?: never;
        post: operations["POST_gitlab_zalopay_vn/phunn4/coupon-api-gateway/internal/handlers_(*VoucherHandler)_HandleCreateVoucher-fm"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/vouchers/auto-eligible": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["POST_gitlab_zalopay_vn/phunn4/coupon-api-gateway/internal/handlers_(*VoucherHandler)_HandleListAutoEligibleVouchers-fm"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/vouchers/check-eligibility": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["POST_gitlab_zalopay_vn/phunn4/coupon-api-gateway/internal/handlers_(*VoucherHandler)_HandleCheckVoucherEligibility-fm"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/vouchers/code/{code}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GET_gitlab_zalopay_vn/phunn4/coupon-api-gateway/internal/handlers_(*VoucherHandler)_HandleGetVoucherByCode-fm"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/vouchers/discount-types": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GET_gitlab_zalopay_vn/phunn4/coupon-api-gateway/internal/handlers_(*VoucherHandler)_HandleGetDiscountTypes-fm"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/vouchers/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GET_gitlab_zalopay_vn/phunn4/coupon-api-gateway/internal/handlers_(*VoucherHandler)_HandleGetVoucher-fm"];
        /** @description Update voucher. Editing is not allowed once the voucher has been used at least once. */
        put: operations["PUT_gitlab_zalopay_vn/phunn4/coupon-api-gateway/internal/handlers_(*VoucherHandler)_HandleUpdateVoucher-fm"];
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/vouchers/{voucher_id}/orders": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["GET_gitlab_zalopay_vn/phunn4/coupon-api-gateway/internal/handlers_(*OrderHandler)_HandleListOrdersByVoucher-fm"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
}
export type webhooks = Record<string, never>;
export interface components {
    schemas: {
        "gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_ListAutoEligibleVouchersResponse_json": {
            /** Format: float64 */
            discount_amount?: number;
            eligible?: boolean;
            voucher?: {
                created_at?: string;
                /** Format: uint64 */
                created_by?: number;
                /** Format: int32 */
                current_usage_count?: number;
                description?: string;
                discount_type?: {
                    /** Format: date-time */
                    created_at?: string;
                    description?: string;
                    /** Format: uint64 */
                    id?: number;
                    is_active?: boolean;
                    type_code?: string;
                    type_name?: string;
                    /** Format: date-time */
                    updated_at?: string;
                };
                /** Format: float64 */
                discount_value?: number;
                /** Format: uint64 */
                id?: number;
                /** Format: float64 */
                max_discount_amount?: number;
                /** Format: int32 */
                max_usage_count?: number;
                /** Format: int32 */
                max_usage_per_user?: number;
                /** Format: float64 */
                min_order_amount?: number;
                product_restrictions?: {
                    /** Format: uint64 */
                    category_id?: number;
                    category_name?: string;
                    created_at?: string;
                    /** Format: uint64 */
                    id?: number;
                    is_included?: boolean;
                    /** Format: uint64 */
                    product_id?: number;
                    product_name?: string;
                    /** Format: uint64 */
                    voucher_id?: number;
                }[];
                status?: string;
                time_restrictions?: {
                    allowed_days_of_week?: number[];
                    /** Format: int32 */
                    allowed_hours_end?: number;
                    /** Format: int32 */
                    allowed_hours_start?: number;
                    created_at?: string;
                    /** Format: uint64 */
                    id?: number;
                    /** Format: int32 */
                    recurrence_day_of_month?: number;
                    /** Format: int32 */
                    recurrence_day_of_week?: number;
                    /** Format: int32 */
                    recurrence_month?: number;
                    recurrence_pattern?: string;
                    restriction_type?: string;
                    specific_dates?: string[];
                    /** Format: uint64 */
                    voucher_id?: number;
                }[];
                title?: string;
                /** Format: float64 */
                total_savings?: number;
                /** Format: int32 */
                unique_users?: number;
                updated_at?: string;
                usage_method?: string;
                user_eligibility?: {
                    created_at?: string;
                    /** Format: uint64 */
                    id?: number;
                    /** Format: int32 */
                    max_account_age_days?: number;
                    /** Format: int64 */
                    max_previous_orders?: number;
                    /** Format: int32 */
                    min_account_age_days?: number;
                    /** Format: int64 */
                    min_previous_orders?: number;
                    /** Format: uint64 */
                    user_id?: number;
                    user_type?: string;
                    /** Format: uint64 */
                    voucher_id?: number;
                }[];
                user_eligibility_type?: string;
                user_usage?: {
                    email?: string;
                    full_name?: string;
                    orders?: {
                        /** Format: float64 */
                        order_amount?: number;
                        /** Format: uint64 */
                        order_id?: number;
                        status?: string;
                        used_at?: string;
                    }[];
                    type?: string;
                    /** Format: int32 */
                    usage_count?: number;
                    /** Format: uint64 */
                    user_id?: number;
                }[];
                valid_from?: string;
                valid_until?: string;
                voucher_code?: string;
            };
        }[];
        "gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_ListCategoriesResponse_json": {
            categories?: {
                /** Format: date-time */
                created_at?: string;
                description?: string;
                /** Format: uint64 */
                id?: number;
                name?: string;
            }[];
        };
        "gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_ListDiscountTypeResponse_json": {
            /** Format: date-time */
            created_at?: string;
            description?: string;
            /** Format: uint64 */
            id?: number;
            is_active?: boolean;
            type_code?: string;
            type_name?: string;
            /** Format: date-time */
            updated_at?: string;
        }[];
        "gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_ListProductsResponse_json": {
            pagination?: {
                /** Format: int32 */
                current_page?: number;
                has_next?: boolean;
                has_previous?: boolean;
                /** Format: int32 */
                page_size?: number;
                /** Format: int64 */
                total_items?: number;
                /** Format: int32 */
                total_pages?: number;
            };
            products?: {
                brand?: string;
                category?: {
                    /** Format: date-time */
                    created_at?: string;
                    description?: string;
                    /** Format: uint64 */
                    id?: number;
                    name?: string;
                };
                /** Format: uint64 */
                category_id?: number;
                /** Format: date-time */
                created_at?: string;
                description?: string;
                /** Format: uint64 */
                id?: number;
                image_url?: string;
                name?: string;
                /** Format: float64 */
                price?: number;
                sku?: string;
                status?: string;
                /** Format: int64 */
                stock_quantity?: number;
                /** Format: date-time */
                updated_at?: string;
            }[];
        };
        "gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_UpdateProductRequestWithID_json": {
            brand?: string;
            /** Format: uint64 */
            category_id?: number;
            description?: string;
            image_url?: string;
            name: string;
            /** Format: float64 */
            price: number;
            sku?: string;
            /** @enum {string} */
            status: "ACTIVE" | "INACTIVE";
            /** Format: int64 */
            stock_quantity?: number;
        };
        "gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_UpdateProductRequestWithID_param": {
            /** Format: uint64 */
            id: number;
        };
        "gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_UpdateVoucherRequestWithID_json": {
            description?: string;
            /** Format: uint64 */
            discount_type_id: number;
            /** Format: float64 */
            discount_value: number;
            /** Format: float64 */
            max_discount_amount?: number;
            /** Format: int32 */
            max_usage_count?: number;
            /** Format: int32 */
            max_usage_per_user?: number;
            /** Format: float64 */
            min_order_amount?: number;
            /** @enum {string} */
            status: "ACTIVE" | "INACTIVE" | "EXPIRED";
            title: string;
            /** @enum {string} */
            usage_method: "MANUAL" | "AUTO";
            /** Format: date-time */
            valid_from: string;
            /** Format: date-time */
            valid_until: string;
        };
        "gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_UpdateVoucherRequestWithID_param": {
            /** Format: uint64 */
            id: number;
        };
        "gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_CheckVoucherEligibilityRequest_json": {
            cart_items?: {
                /** Format: uint64 */
                category_id?: number;
                /** Format: float64 */
                price: number;
                /** Format: uint64 */
                product_id?: number;
                /** Format: int32 */
                quantity: number;
            }[];
            /** Format: float64 */
            order_amount: number;
            /** Format: date-time */
            order_timestamp?: string;
            voucher_code: string;
        };
        "gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_CreateOrderRequest_json": {
            items: {
                /** Format: float64 */
                price: number;
                /** Format: uint64 */
                product_id: number;
                /** Format: int32 */
                quantity: number;
            }[];
            /** Format: float64 */
            order_amount: number;
            /** Format: date-time */
            order_timestamp: string;
            /** Format: uint64 */
            user_id: number;
            voucher_code?: string;
        };
        "gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_CreateVoucherRequest_json": {
            description?: string;
            /** Format: uint64 */
            discount_type_id: number;
            /** Format: float64 */
            discount_value: number;
            /** Format: float64 */
            max_discount_amount?: number;
            /** Format: int32 */
            max_usage_count?: number;
            /** Format: int32 */
            max_usage_per_user?: number;
            /** Format: float64 */
            min_order_amount?: number;
            title: string;
            /** @enum {string} */
            usage_method: "MANUAL" | "AUTO";
            /** Format: date-time */
            valid_from: string;
            /** Format: date-time */
            valid_until: string;
            voucher_code: string;
        };
        "gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_ListAutoEligibleVouchersRequest_json": {
            cart_items?: {
                /** Format: uint64 */
                category_id?: number;
                /** Format: float64 */
                price: number;
                /** Format: uint64 */
                product_id?: number;
                /** Format: int32 */
                quantity: number;
            }[];
            /** Format: float64 */
            order_amount: number;
            /** Format: date-time */
            order_timestamp?: string;
        };
        "gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_ListOrdersResponse_json": {
            orders?: {
                applied_voucher_code?: string;
                /** Format: uint64 */
                applied_voucher_id?: number;
                calculation_message?: string;
                calculation_status?: string;
                /** Format: date-time */
                created_at?: string;
                /** Format: float64 */
                discount_amount?: number;
                /** Format: uint64 */
                id?: number;
                items?: {
                    /** Format: float64 */
                    price?: number;
                    /** Format: uint64 */
                    product_id?: number;
                    /** Format: int32 */
                    quantity?: number;
                }[];
                /** Format: float64 */
                order_amount?: number;
                /** Format: date-time */
                updated_at?: string;
                /** Format: uint64 */
                user_id?: number;
            }[];
            pagination?: {
                /** Format: int32 */
                current_page?: number;
                has_next?: boolean;
                has_previous?: boolean;
                /** Format: int32 */
                page_size?: number;
                /** Format: int64 */
                total_items?: number;
                /** Format: int32 */
                total_pages?: number;
            };
        };
        "gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_ListProductsRequest_json": {
            /** Format: uint64 */
            category_id?: number;
            /** Format: int32 */
            limit?: number;
            /** Format: int32 */
            page?: number;
            search?: string;
            sort_by?: string;
            sort_order?: string;
            status?: string;
        };
        "gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_ListProductsRequest_query": {
            /** Format: uint64 */
            category_id?: number;
            /** Format: int32 */
            limit?: number;
            /** Format: int32 */
            page?: number;
            search?: string;
            sort_by?: string;
            sort_order?: string;
            status?: string;
        };
        "gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_ListVouchersRequest_json": {
            /** Format: uint64 */
            discount_type_id?: number;
            /** Format: int32 */
            limit?: number;
            /** Format: int32 */
            page?: number;
            search?: string;
            sort_by?: string;
            sort_order?: string;
            status?: string;
            usage_method?: string;
        };
        "gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_ListVouchersRequest_query": {
            /** Format: uint64 */
            discount_type_id?: number;
            /** Format: int32 */
            limit?: number;
            /** Format: int32 */
            page?: number;
            search?: string;
            sort_by?: string;
            sort_order?: string;
            status?: string;
            usage_method?: string;
        };
        "gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_ListVouchersResponse_json": {
            data?: {
                created_at?: string;
                /** Format: uint64 */
                created_by?: number;
                discount_type_code?: string;
                discount_type_name?: string;
                /** Format: float64 */
                discount_value?: number;
                /** Format: uint64 */
                id?: number;
                status?: string;
                title?: string;
                updated_at?: string;
                usage_method?: string;
                valid_from?: string;
                valid_until?: string;
                voucher_code?: string;
            }[];
            /** Format: int32 */
            limit?: number;
            /** Format: int32 */
            page?: number;
            /** Format: int32 */
            total?: number;
            /** Format: int32 */
            total_pages?: number;
        };
        "gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_LoginRequest_json": {
            /** Format: email */
            email: string;
            password: string;
        };
        "gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_LoginResponse_json": {
            email?: string;
            /** Format: uint64 */
            id?: number;
            name?: string;
            role?: string;
            type?: string;
        };
        "gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_OrderResponse_json": {
            applied_voucher_code?: string;
            /** Format: uint64 */
            applied_voucher_id?: number;
            calculation_message?: string;
            calculation_status?: string;
            /** Format: date-time */
            created_at?: string;
            /** Format: float64 */
            discount_amount?: number;
            /** Format: uint64 */
            id?: number;
            items?: {
                /** Format: float64 */
                price?: number;
                /** Format: uint64 */
                product_id?: number;
                /** Format: int32 */
                quantity?: number;
            }[];
            /** Format: float64 */
            order_amount?: number;
            /** Format: date-time */
            updated_at?: string;
            /** Format: uint64 */
            user_id?: number;
        };
        "gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_ProductResponse_json": {
            brand?: string;
            category?: {
                /** Format: date-time */
                created_at?: string;
                description?: string;
                /** Format: uint64 */
                id?: number;
                name?: string;
            };
            /** Format: uint64 */
            category_id?: number;
            /** Format: date-time */
            created_at?: string;
            description?: string;
            /** Format: uint64 */
            id?: number;
            image_url?: string;
            name?: string;
            /** Format: float64 */
            price?: number;
            sku?: string;
            status?: string;
            /** Format: int64 */
            stock_quantity?: number;
            /** Format: date-time */
            updated_at?: string;
        };
        "gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_RegisterRequest_json": {
            /** Format: email */
            email: string;
            name: string;
            password: string;
        };
        "gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_RegisterResponse_json": {
            email?: string;
            /** Format: uint64 */
            id?: number;
            name?: string;
            role?: string;
            type?: string;
        };
        "gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_UpdateOrderStatusRequest_json": {
            /** Format: uint64 */
            order_id: number;
            status: string;
        };
        "gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_UserOrderCountResponse_json": {
            /** Format: int64 */
            order_count?: number;
        };
        "gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_UserResponse_json": {
            /** Format: date-time */
            created_at?: string;
            email?: string;
            /** Format: uint64 */
            id?: number;
            name?: string;
            role?: string;
            type?: string;
        };
        "gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_UserVoucherUsageCountResponse_json": {
            /** Format: int32 */
            usage_count?: number;
        };
        "gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_VoucherEligibilityResponse_json": {
            /** Format: float64 */
            discount_amount?: number;
            eligible?: boolean;
            message?: string;
            /** Format: uint64 */
            voucher_id?: number;
        };
        "gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_VoucherResponse_json": {
            created_at?: string;
            /** Format: uint64 */
            created_by?: number;
            /** Format: int32 */
            current_usage_count?: number;
            description?: string;
            discount_type?: {
                /** Format: date-time */
                created_at?: string;
                description?: string;
                /** Format: uint64 */
                id?: number;
                is_active?: boolean;
                type_code?: string;
                type_name?: string;
                /** Format: date-time */
                updated_at?: string;
            };
            /** Format: float64 */
            discount_value?: number;
            /** Format: uint64 */
            id?: number;
            /** Format: float64 */
            max_discount_amount?: number;
            /** Format: int32 */
            max_usage_count?: number;
            /** Format: int32 */
            max_usage_per_user?: number;
            /** Format: float64 */
            min_order_amount?: number;
            product_restrictions?: {
                /** Format: uint64 */
                category_id?: number;
                category_name?: string;
                created_at?: string;
                /** Format: uint64 */
                id?: number;
                is_included?: boolean;
                /** Format: uint64 */
                product_id?: number;
                product_name?: string;
                /** Format: uint64 */
                voucher_id?: number;
            }[];
            status?: string;
            time_restrictions?: {
                allowed_days_of_week?: number[];
                /** Format: int32 */
                allowed_hours_end?: number;
                /** Format: int32 */
                allowed_hours_start?: number;
                created_at?: string;
                /** Format: uint64 */
                id?: number;
                /** Format: int32 */
                recurrence_day_of_month?: number;
                /** Format: int32 */
                recurrence_day_of_week?: number;
                /** Format: int32 */
                recurrence_month?: number;
                recurrence_pattern?: string;
                restriction_type?: string;
                specific_dates?: string[];
                /** Format: uint64 */
                voucher_id?: number;
            }[];
            title?: string;
            /** Format: float64 */
            total_savings?: number;
            /** Format: int32 */
            unique_users?: number;
            updated_at?: string;
            usage_method?: string;
            user_eligibility?: {
                created_at?: string;
                /** Format: uint64 */
                id?: number;
                /** Format: int32 */
                max_account_age_days?: number;
                /** Format: int64 */
                max_previous_orders?: number;
                /** Format: int32 */
                min_account_age_days?: number;
                /** Format: int64 */
                min_previous_orders?: number;
                /** Format: uint64 */
                user_id?: number;
                user_type?: string;
                /** Format: uint64 */
                voucher_id?: number;
            }[];
            user_eligibility_type?: string;
            user_usage?: {
                email?: string;
                full_name?: string;
                orders?: {
                    /** Format: float64 */
                    order_amount?: number;
                    /** Format: uint64 */
                    order_id?: number;
                    status?: string;
                    used_at?: string;
                }[];
                type?: string;
                /** Format: int32 */
                usage_count?: number;
                /** Format: uint64 */
                user_id?: number;
            }[];
            valid_from?: string;
            valid_until?: string;
            voucher_code?: string;
        };
        "gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_productIDParams_param": {
            /** Format: uint64 */
            id: number;
        };
        "gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_voucherCodeParams_param": {
            /** Format: int32 */
            code: number;
        };
        "gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_voucherIDParams_param": {
            /** Format: int32 */
            id: number;
        };
        "gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_utils_SuccessResponse_json": {
            message?: string;
        };
    };
    responses: never;
    parameters: never;
    requestBodies: never;
    headers: never;
    pathItems: never;
}
export type $defs = Record<string, never>;
export interface operations {
    "GET_gitlab_zalopay_vn/phunn4/coupon-api-gateway/internal/handlers_(*ProductHandler)_HandleListCategories-fm": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_ListCategoriesResponse_json"];
                };
            };
        };
    };
    "POST_gitlab_zalopay_vn/phunn4/coupon-api-gateway/internal/handlers_(*AuthHandler)_HandleLogin-fm": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_LoginRequest_json"];
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_LoginResponse_json"];
                };
            };
        };
    };
    "POST_gitlab_zalopay_vn/phunn4/coupon-api-gateway/internal/handlers_(*AuthHandler)_HandleLogout-fm": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_utils_SuccessResponse_json"];
                };
            };
        };
    };
    "GET_gitlab_zalopay_vn/phunn4/coupon-api-gateway/internal/handlers_(*OrderHandler)_HandleListOrders-fm": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_ListOrdersResponse_json"];
                };
            };
        };
    };
    "POST_gitlab_zalopay_vn/phunn4/coupon-api-gateway/internal/handlers_(*OrderHandler)_HandleCreateOrder-fm": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_CreateOrderRequest_json"];
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_OrderResponse_json"];
                };
            };
        };
    };
    "GET_gitlab_zalopay_vn/phunn4/coupon-api-gateway/internal/handlers_(*OrderHandler)_HandleGetOrder-fm": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_OrderResponse_json"];
                };
            };
        };
    };
    "PUT_gitlab_zalopay_vn/phunn4/coupon-api-gateway/internal/handlers_(*OrderHandler)_HandleUpdateOrderStatus-fm": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_UpdateOrderStatusRequest_json"];
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_OrderResponse_json"];
                };
            };
        };
    };
    "GET_gitlab_zalopay_vn/phunn4/coupon-api-gateway/internal/handlers_(*ProductHandler)_HandleListProducts-fm": {
        parameters: {
            query?: {
                page?: components["schemas"]["gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_ListProductsRequest_query"]["page"];
                limit?: components["schemas"]["gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_ListProductsRequest_query"]["limit"];
                search?: components["schemas"]["gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_ListProductsRequest_query"]["search"];
                category_id?: components["schemas"]["gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_ListProductsRequest_query"]["category_id"];
                status?: components["schemas"]["gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_ListProductsRequest_query"]["status"];
                sort_by?: components["schemas"]["gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_ListProductsRequest_query"]["sort_by"];
                sort_order?: components["schemas"]["gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_ListProductsRequest_query"]["sort_order"];
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_ListProductsResponse_json"];
                };
            };
        };
    };
    "GET_gitlab_zalopay_vn/phunn4/coupon-api-gateway/internal/handlers_(*ProductHandler)_HandleGetProduct-fm": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: components["schemas"]["gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_productIDParams_param"]["id"];
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_ProductResponse_json"];
                };
            };
        };
    };
    "PUT_gitlab_zalopay_vn/phunn4/coupon-api-gateway/internal/handlers_(*ProductHandler)_HandleUpdateProduct-fm": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: components["schemas"]["gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_UpdateProductRequestWithID_param"]["id"];
            };
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_UpdateProductRequestWithID_json"];
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_ProductResponse_json"];
                };
            };
        };
    };
    "POST_gitlab_zalopay_vn/phunn4/coupon-api-gateway/internal/handlers_(*AuthHandler)_HandleRegister-fm": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_RegisterRequest_json"];
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_RegisterResponse_json"];
                };
            };
        };
    };
    "GET_gitlab_zalopay_vn/phunn4/coupon-api-gateway/internal/handlers_(*UserHandler)_HandleGetMe-fm": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_UserResponse_json"];
                };
            };
        };
    };
    "GET_gitlab_zalopay_vn/phunn4/coupon-api-gateway/internal/handlers_(*OrderHandler)_HandleGetUserOrderCount-fm": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_UserOrderCountResponse_json"];
                };
            };
        };
    };
    "GET_gitlab_zalopay_vn/phunn4/coupon-api-gateway/internal/handlers_(*OrderHandler)_HandleGetUserVoucherUsageCount-fm": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_UserVoucherUsageCountResponse_json"];
                };
            };
        };
    };
    "GET_gitlab_zalopay_vn/phunn4/coupon-api-gateway/internal/handlers_(*VoucherHandler)_HandleListVouchers-fm": {
        parameters: {
            query?: {
                usage_method?: components["schemas"]["gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_ListVouchersRequest_query"]["usage_method"];
                status?: components["schemas"]["gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_ListVouchersRequest_query"]["status"];
                sort_by?: components["schemas"]["gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_ListVouchersRequest_query"]["sort_by"];
                sort_order?: components["schemas"]["gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_ListVouchersRequest_query"]["sort_order"];
                page?: components["schemas"]["gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_ListVouchersRequest_query"]["page"];
                limit?: components["schemas"]["gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_ListVouchersRequest_query"]["limit"];
                search?: components["schemas"]["gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_ListVouchersRequest_query"]["search"];
                discount_type_id?: components["schemas"]["gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_ListVouchersRequest_query"]["discount_type_id"];
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_ListVouchersResponse_json"];
                };
            };
        };
    };
    "POST_gitlab_zalopay_vn/phunn4/coupon-api-gateway/internal/handlers_(*VoucherHandler)_HandleCreateVoucher-fm": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_CreateVoucherRequest_json"];
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_VoucherResponse_json"];
                };
            };
        };
    };
    "POST_gitlab_zalopay_vn/phunn4/coupon-api-gateway/internal/handlers_(*VoucherHandler)_HandleListAutoEligibleVouchers-fm": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_ListAutoEligibleVouchersRequest_json"];
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_ListAutoEligibleVouchersResponse_json"];
                };
            };
        };
    };
    "POST_gitlab_zalopay_vn/phunn4/coupon-api-gateway/internal/handlers_(*VoucherHandler)_HandleCheckVoucherEligibility-fm": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_CheckVoucherEligibilityRequest_json"];
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_VoucherEligibilityResponse_json"];
                };
            };
        };
    };
    "GET_gitlab_zalopay_vn/phunn4/coupon-api-gateway/internal/handlers_(*VoucherHandler)_HandleGetVoucherByCode-fm": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                code: components["schemas"]["gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_voucherCodeParams_param"]["code"];
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_VoucherResponse_json"];
                };
            };
        };
    };
    "GET_gitlab_zalopay_vn/phunn4/coupon-api-gateway/internal/handlers_(*VoucherHandler)_HandleGetDiscountTypes-fm": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_ListDiscountTypeResponse_json"];
                };
            };
        };
    };
    "GET_gitlab_zalopay_vn/phunn4/coupon-api-gateway/internal/handlers_(*VoucherHandler)_HandleGetVoucher-fm": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: components["schemas"]["gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_voucherIDParams_param"]["id"];
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_VoucherResponse_json"];
                };
            };
        };
    };
    "PUT_gitlab_zalopay_vn/phunn4/coupon-api-gateway/internal/handlers_(*VoucherHandler)_HandleUpdateVoucher-fm": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: components["schemas"]["gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_UpdateVoucherRequestWithID_param"]["id"];
            };
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_UpdateVoucherRequestWithID_json"];
            };
        };
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_VoucherResponse_json"];
                };
            };
        };
    };
    "GET_gitlab_zalopay_vn/phunn4/coupon-api-gateway/internal/handlers_(*OrderHandler)_HandleListOrdersByVoucher-fm": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["gitlab_zalopay_vn_phunn4_coupon-api-gateway_internal_handlers_dto_ListOrdersResponse_json"];
                };
            };
        };
    };
}
